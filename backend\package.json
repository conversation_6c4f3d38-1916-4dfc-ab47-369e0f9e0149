{"name": "playstation-shop-backend", "version": "1.0.0", "description": "Backend API for PlayStation Shop Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["playstation", "shop", "management", "api", "arabic"], "author": "PlayStation Shop Management System", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "joi": "^17.11.0", "moment": "^2.29.4", "socket.io": "^4.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}