import dayjs from 'dayjs'
import 'dayjs/locale/ar'
import relativeTime from 'dayjs/plugin/relativeTime'
import duration from 'dayjs/plugin/duration'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

// Configure dayjs
dayjs.extend(relativeTime)
dayjs.extend(duration)
dayjs.extend(timezone)
dayjs.extend(utc)
dayjs.locale('ar')

// Date and time utilities
export const dateUtils = {
  // Format date for display
  formatDate: (date, format = 'YYYY-MM-DD') => {
    return dayjs(date).format(format)
  },

  // Format time for display
  formatTime: (date, format = 'HH:mm') => {
    return dayjs(date).format(format)
  },

  // Format datetime for display
  formatDateTime: (date, format = 'YYYY-MM-DD HH:mm') => {
    return dayjs(date).format(format)
  },

  // Format date in Arabic
  formatDateArabic: (date) => {
    return dayjs(date).format('dddd، D MMMM YYYY')
  },

  // Format time in Arabic
  formatTimeArabic: (date) => {
    return dayjs(date).format('HH:mm')
  },

  // Get relative time (e.g., "منذ ساعتين")
  fromNow: (date) => {
    return dayjs(date).fromNow()
  },

  // Get time until (e.g., "خلال ساعتين")
  toNow: (date) => {
    return dayjs(date).toNow()
  },

  // Calculate duration between two dates
  getDuration: (startDate, endDate) => {
    return dayjs(endDate).diff(dayjs(startDate))
  },

  // Format duration in Arabic
  formatDuration: (milliseconds) => {
    const duration = dayjs.duration(milliseconds)
    const hours = Math.floor(duration.asHours())
    const minutes = duration.minutes()
    
    if (hours === 0) {
      return `${minutes} دقيقة`
    } else if (minutes === 0) {
      return `${hours} ساعة`
    } else {
      return `${hours} ساعة و ${minutes} دقيقة`
    }
  },

  // Check if date is today
  isToday: (date) => {
    return dayjs(date).isSame(dayjs(), 'day')
  },

  // Check if date is yesterday
  isYesterday: (date) => {
    return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day')
  },

  // Get start of day
  startOfDay: (date = new Date()) => {
    return dayjs(date).startOf('day').toDate()
  },

  // Get end of day
  endOfDay: (date = new Date()) => {
    return dayjs(date).endOf('day').toDate()
  }
}

// Number and currency utilities
export const numberUtils = {
  // Format currency
  formatCurrency: (amount, currency = 'EGP') => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  },

  // Format number with Arabic numerals
  formatNumber: (number) => {
    return new Intl.NumberFormat('ar-EG').format(number)
  },

  // Round to decimal places
  round: (num, decimals = 2) => {
    return Math.round((num + Number.EPSILON) * Math.pow(10, decimals)) / Math.pow(10, decimals)
  },

  // Calculate percentage
  percentage: (value, total) => {
    if (total === 0) return 0
    return Math.round((value / total) * 100)
  },

  // Format percentage
  formatPercentage: (value, total) => {
    const percent = numberUtils.percentage(value, total)
    return `${percent}%`
  },

  // Calculate tax
  calculateTax: (amount, taxRate = 0.14) => {
    return numberUtils.round(amount * taxRate)
  },

  // Calculate discount
  calculateDiscount: (amount, discountPercent) => {
    return numberUtils.round(amount * (discountPercent / 100))
  },

  // Format file size
  formatFileSize: (bytes) => {
    if (bytes === 0) return '0 بايت'
    
    const k = 1024
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

// String utilities
export const stringUtils = {
  // Truncate string
  truncate: (str, length = 50) => {
    if (!str) return ''
    if (str.length <= length) return str
    return str.substring(0, length) + '...'
  },

  // Capitalize first letter
  capitalize: (str) => {
    if (!str) return ''
    return str.charAt(0).toUpperCase() + str.slice(1)
  },

  // Generate random ID
  generateId: (prefix = '', length = 8) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let result = prefix
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  },

  // Clean phone number
  cleanPhone: (phone) => {
    if (!phone) return ''
    return phone.replace(/[\s\-\(\)]/g, '')
  },

  // Format phone number for display
  formatPhone: (phone) => {
    if (!phone) return ''
    const cleaned = stringUtils.cleanPhone(phone)
    
    // Egyptian phone number format
    if (cleaned.startsWith('+20')) {
      return cleaned.replace(/(\+20)(\d{2})(\d{4})(\d{4})/, '$1 $2 $3 $4')
    } else if (cleaned.startsWith('01')) {
      return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
    }
    
    return phone
  },

  // Validate email
  isValidEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  // Validate Egyptian phone
  isValidEgyptianPhone: (phone) => {
    const cleaned = stringUtils.cleanPhone(phone)
    const phoneRegex = /^(\+20|0)?1[0125]\d{8}$/
    return phoneRegex.test(cleaned)
  },

  // Search highlight
  highlightSearch: (text, searchTerm) => {
    if (!searchTerm) return text
    
    const regex = new RegExp(`(${searchTerm})`, 'gi')
    return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>')
  }
}

// Array utilities
export const arrayUtils = {
  // Remove duplicates
  unique: (arr) => {
    return [...new Set(arr)]
  },

  // Group by key
  groupBy: (arr, key) => {
    return arr.reduce((groups, item) => {
      const group = item[key]
      groups[group] = groups[group] || []
      groups[group].push(item)
      return groups
    }, {})
  },

  // Sort by multiple keys
  sortBy: (arr, keys) => {
    return arr.sort((a, b) => {
      for (let key of keys) {
        const aVal = a[key]
        const bVal = b[key]
        if (aVal < bVal) return -1
        if (aVal > bVal) return 1
      }
      return 0
    })
  },

  // Chunk array into smaller arrays
  chunk: (arr, size) => {
    const chunks = []
    for (let i = 0; i < arr.length; i += size) {
      chunks.push(arr.slice(i, i + size))
    }
    return chunks
  },

  // Find item by property
  findBy: (arr, key, value) => {
    return arr.find(item => item[key] === value)
  },

  // Filter by multiple criteria
  filterBy: (arr, criteria) => {
    return arr.filter(item => {
      return Object.keys(criteria).every(key => {
        if (Array.isArray(criteria[key])) {
          return criteria[key].includes(item[key])
        }
        return item[key] === criteria[key]
      })
    })
  }
}

// Object utilities
export const objectUtils = {
  // Deep clone
  deepClone: (obj) => {
    return JSON.parse(JSON.stringify(obj))
  },

  // Pick specific keys
  pick: (obj, keys) => {
    const result = {}
    keys.forEach(key => {
      if (obj.hasOwnProperty(key)) {
        result[key] = obj[key]
      }
    })
    return result
  },

  // Omit specific keys
  omit: (obj, keys) => {
    const result = { ...obj }
    keys.forEach(key => {
      delete result[key]
    })
    return result
  },

  // Check if object is empty
  isEmpty: (obj) => {
    return Object.keys(obj).length === 0
  },

  // Flatten nested object
  flatten: (obj, prefix = '') => {
    const flattened = {}
    
    Object.keys(obj).forEach(key => {
      const value = obj[key]
      const newKey = prefix ? `${prefix}.${key}` : key
      
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        Object.assign(flattened, objectUtils.flatten(value, newKey))
      } else {
        flattened[newKey] = value
      }
    })
    
    return flattened
  }
}

// Local storage utilities
export const storageUtils = {
  // Set item with expiration
  setWithExpiry: (key, value, ttl) => {
    const now = new Date()
    const item = {
      value: value,
      expiry: now.getTime() + ttl
    }
    localStorage.setItem(key, JSON.stringify(item))
  },

  // Get item with expiration check
  getWithExpiry: (key) => {
    const itemStr = localStorage.getItem(key)
    
    if (!itemStr) {
      return null
    }
    
    const item = JSON.parse(itemStr)
    const now = new Date()
    
    if (now.getTime() > item.expiry) {
      localStorage.removeItem(key)
      return null
    }
    
    return item.value
  },

  // Clear expired items
  clearExpired: () => {
    const now = new Date()
    
    Object.keys(localStorage).forEach(key => {
      try {
        const itemStr = localStorage.getItem(key)
        const item = JSON.parse(itemStr)
        
        if (item.expiry && now.getTime() > item.expiry) {
          localStorage.removeItem(key)
        }
      } catch (e) {
        // Not a JSON item, skip
      }
    })
  }
}

// Validation utilities
export const validationUtils = {
  // Required field
  required: (value) => {
    return value !== null && value !== undefined && value !== ''
  },

  // Minimum length
  minLength: (value, min) => {
    return value && value.length >= min
  },

  // Maximum length
  maxLength: (value, max) => {
    return !value || value.length <= max
  },

  // Email validation
  email: (value) => {
    return stringUtils.isValidEmail(value)
  },

  // Phone validation
  phone: (value) => {
    return stringUtils.isValidEgyptianPhone(value)
  },

  // Number validation
  number: (value) => {
    return !isNaN(value) && isFinite(value)
  },

  // Positive number
  positive: (value) => {
    return validationUtils.number(value) && value > 0
  },

  // Range validation
  range: (value, min, max) => {
    return validationUtils.number(value) && value >= min && value <= max
  }
}

// Export all utilities
export default {
  dateUtils,
  numberUtils,
  stringUtils,
  arrayUtils,
  objectUtils,
  storageUtils,
  validationUtils
}
