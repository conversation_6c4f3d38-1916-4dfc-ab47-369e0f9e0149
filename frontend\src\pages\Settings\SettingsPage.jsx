import { motion } from 'framer-motion'
import { CogIcon } from '@heroicons/react/24/outline'

const SettingsPage = () => {
  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-sm p-8 border border-gray-200 text-center"
      >
        <CogIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h1 className="text-2xl font-bold text-gray-900 mb-2">إعدادات النظام</h1>
        <p className="text-gray-600 mb-6">
          هذه الصفحة ستحتوي على إعدادات النظام والتخصيص
        </p>
        <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
          <p className="text-indigo-800 text-sm">
            🚧 هذه الصفحة قيد التطوير وستكون متاحة قريباً
          </p>
        </div>
      </motion.div>
    </div>
  )
}

export default SettingsPage
