import { Navigate, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useAuthStore } from '../../store/authStore'
import LoadingSpinner from '../UI/LoadingSpinner'

const ProtectedRoute = ({ 
  children, 
  requiredPermission = null,
  requiredRole = null,
  fallbackPath = '/login' 
}) => {
  const { user, isAuthenticated, isLoading, hasPermission } = useAuthStore()
  const location = useLocation()

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <LoadingSpinner size="large" />
          <p className="mt-4 text-gray-600 font-medium">جاري التحقق من الصلاحيات...</p>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated || !user) {
    return (
      <Navigate 
        to={fallbackPath} 
        state={{ from: location }} 
        replace 
      />
    )
  }

  // Check role requirement
  if (requiredRole && user.role !== requiredRole && user.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center bg-white rounded-lg shadow-lg p-8 max-w-md mx-4"
        >
          <div className="text-6xl mb-4">🚫</div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">
            غير مخول للوصول
          </h1>
          <p className="text-gray-600 mb-6">
            ليس لديك الصلاحية المطلوبة للوصول لهذه الصفحة
          </p>
          <p className="text-sm text-gray-500 mb-4">
            الصلاحية المطلوبة: {requiredRole}
          </p>
          <button
            onClick={() => window.history.back()}
            className="btn-primary"
          >
            العودة للخلف
          </button>
        </motion.div>
      </div>
    )
  }

  // Check permission requirement
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center bg-white rounded-lg shadow-lg p-8 max-w-md mx-4"
        >
          <div className="text-6xl mb-4">🔒</div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">
            غير مخول للوصول
          </h1>
          <p className="text-gray-600 mb-6">
            ليس لديك الصلاحية المطلوبة للوصول لهذه الصفحة
          </p>
          <p className="text-sm text-gray-500 mb-4">
            الصلاحية المطلوبة: {getPermissionDisplayName(requiredPermission)}
          </p>
          <div className="flex gap-3 justify-center">
            <button
              onClick={() => window.history.back()}
              className="btn-secondary"
            >
              العودة للخلف
            </button>
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="btn-primary"
            >
              الذهاب للوحة التحكم
            </button>
          </div>
        </motion.div>
      </div>
    )
  }

  // User is authenticated and has required permissions
  return children
}

// Helper function to get permission display name in Arabic
const getPermissionDisplayName = (permission) => {
  const permissionNames = {
    canManageConsoles: 'إدارة الأجهزة',
    canManageSessions: 'إدارة الجلسات',
    canManageCustomers: 'إدارة العملاء',
    canViewReports: 'عرض التقارير',
    canManageUsers: 'إدارة المستخدمين',
    canManageSettings: 'إدارة الإعدادات'
  }
  
  return permissionNames[permission] || permission
}

// Higher-order component for role-based access
export const withRoleAccess = (Component, requiredRole) => {
  return (props) => (
    <ProtectedRoute requiredRole={requiredRole}>
      <Component {...props} />
    </ProtectedRoute>
  )
}

// Higher-order component for permission-based access
export const withPermissionAccess = (Component, requiredPermission) => {
  return (props) => (
    <ProtectedRoute requiredPermission={requiredPermission}>
      <Component {...props} />
    </ProtectedRoute>
  )
}

// Hook for checking permissions in components
export const usePermissionCheck = () => {
  const { hasPermission, user } = useAuthStore()
  
  return {
    hasPermission,
    isAdmin: user?.role === 'admin',
    isStaff: user?.role === 'staff' || user?.role === 'admin',
    canManageConsoles: hasPermission('canManageConsoles'),
    canManageSessions: hasPermission('canManageSessions'),
    canManageCustomers: hasPermission('canManageCustomers'),
    canViewReports: hasPermission('canViewReports'),
    canManageUsers: hasPermission('canManageUsers'),
    canManageSettings: hasPermission('canManageSettings')
  }
}

export default ProtectedRoute
