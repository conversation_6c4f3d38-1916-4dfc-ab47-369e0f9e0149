# 🔧 دليل حل المشاكل / Troubleshooting Guide

## ❌ خطأ: "خطأ في الاتصال بالخادم، يرجى التحقق من الاتصال بالإنترنت"

### السبب المحتمل / Possible Cause:
Frontend لا يستطيع الاتصال بـ Backend

### الحلول / Solutions:

#### 🔍 الحل 1: تشخيص المشكلة
```bash
# شغل ملف التشخيص / Run diagnosis file
diagnose.bat
```

#### 🚀 الحل 2: استخدام ملف التشغيل المحسن
```bash
# استخدم الملف المحسن / Use improved start file
start-fixed.bat
```

#### 🎮 الحل 3: تشغيل بدون قاعدة بيانات (للاختبار)
```bash
# للاختبار السريع / For quick testing
start-no-db.bat
```

#### 🔧 الحل 4: التشغيل اليدوي خطوة بخطوة

##### الخطوة 1: تحقق من Node.js
```bash
node --version
npm --version
```
إذا لم يعمل، حمل Node.js من: https://nodejs.org/

##### الخطوة 2: تثبيت التبعيات
```bash
# Backend
cd backend
npm install

# Frontend (في terminal جديد)
cd frontend
npm install
```

##### الخطوة 3: إنشاء ملفات .env
**backend/.env:**
```env
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/playstation-shop
JWT_SECRET=your-super-secret-jwt-key-2024
JWT_EXPIRES_IN=7d
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=admin123456
FRONTEND_URL=http://localhost:3000
```

**frontend/.env:**
```env
VITE_API_URL=http://localhost:5000/api
VITE_APP_NAME=PlayStation Shop Management
VITE_APP_VERSION=1.0.0
```

##### الخطوة 4: تشغيل Backend أولاً
```bash
cd backend
npm run dev
```
انتظر حتى ترى: "🚀 Server running on port 5000"

##### الخطوة 5: تشغيل Frontend
```bash
cd frontend
npm run dev
```

##### الخطوة 6: اختبار الاتصال
افتح: http://localhost:5000/api/health
يجب أن ترى رسالة نجاح

---

## ❌ مشاكل أخرى شائعة / Other Common Issues

### 🔴 خطأ: "Port 5000 is already in use"
```bash
# إيقاف العملية المستخدمة للمنفذ / Kill process using port
netstat -ano | findstr :5000
taskkill /PID [PID_NUMBER] /F
```

### 🔴 خطأ: "MongoDB connection error"
**الحلول:**
1. تثبيت MongoDB محلياً
2. استخدام MongoDB Atlas (مجاني)
3. تشغيل النظام بدون قاعدة بيانات: `start-no-db.bat`

### 🔴 خطأ: "npm command not found"
- تأكد من تثبيت Node.js بشكل صحيح
- أعد تشغيل Command Prompt
- أضف Node.js لـ PATH

### 🔴 خطأ: "Module not found"
```bash
# احذف node_modules وأعد التثبيت
rm -rf node_modules package-lock.json
npm install
```

### 🔴 Frontend لا يفتح
- تأكد من أن المنفذ 3000 غير مستخدم
- جرب منفذ آخر: `npm run dev -- --port 3001`

---

## 🛠️ أدوات التشخيص / Diagnostic Tools

### 1. فحص الاتصال / Connection Test
```bash
# اختبار Backend
curl http://localhost:5000/api/health

# اختبار Frontend
curl http://localhost:3000
```

### 2. فحص المنافذ / Port Check
```bash
# فحص المنافذ المستخدمة
netstat -an | find "5000"
netstat -an | find "3000"
```

### 3. فحص العمليات / Process Check
```bash
# عرض العمليات المستخدمة للمنافذ
netstat -ano | findstr :5000
netstat -ano | findstr :3000
```

---

## 📋 قائمة التحقق / Checklist

قبل طلب المساعدة، تأكد من:

- [ ] Node.js مثبت (الإصدار 16+)
- [ ] npm يعمل بشكل صحيح
- [ ] ملفات .env موجودة في المجلدين
- [ ] تم تثبيت التبعيات في المجلدين
- [ ] Backend يعمل على المنفذ 5000
- [ ] Frontend يعمل على المنفذ 3000
- [ ] لا توجد عمليات أخرى تستخدم المنافذ
- [ ] Firewall لا يحجب المنافذ

---

## 🆘 طلب المساعدة / Getting Help

إذا لم تحل المشكلة:

1. **شغل ملف التشخيص:** `diagnose.bat`
2. **احفظ نتائج التشخيص**
3. **جرب الحلول المقترحة**
4. **إذا استمرت المشكلة، أرسل:**
   - نتائج التشخيص
   - رسائل الخطأ
   - نظام التشغيل
   - إصدار Node.js

---

## 🎯 نصائح للأداء الأفضل / Performance Tips

### للتطوير / Development:
- استخدم SSD للمشروع
- أغلق البرامج غير الضرورية
- استخدم terminal حديث (Windows Terminal)

### للإنتاج / Production:
- استخدم PM2 لإدارة العمليات
- فعل HTTPS
- استخدم قاعدة بيانات منفصلة
- فعل النسخ الاحتياطي

---

## 🔄 إعادة التثبيت الكاملة / Complete Reinstall

إذا فشل كل شيء:

```bash
# 1. احذف المجلدات
rm -rf backend/node_modules
rm -rf frontend/node_modules
rm -rf backend/package-lock.json
rm -rf frontend/package-lock.json

# 2. أعد التثبيت
cd backend && npm install
cd ../frontend && npm install

# 3. أعد إنشاء ملفات .env
# (انسخ المحتوى من الأعلى)

# 4. شغل النظام
start-fixed.bat
```
