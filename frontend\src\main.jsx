import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import App from './App.jsx'
import './index.css'

// Configure toast notifications for RTL
const toastOptions = {
  duration: 4000,
  position: 'top-center',
  style: {
    fontFamily: 'Cairo, Tajawal, system-ui, sans-serif',
    direction: 'rtl',
    textAlign: 'right',
    fontSize: '14px'
  },
  success: {
    iconTheme: {
      primary: '#22c55e',
      secondary: '#ffffff'
    }
  },
  error: {
    iconTheme: {
      primary: '#ef4444',
      secondary: '#ffffff'
    }
  }
}

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <BrowserRouter>
      <App />
      <Toaster toastOptions={toastOptions} />
    </BrowserRouter>
  </React.StrictMode>,
)
