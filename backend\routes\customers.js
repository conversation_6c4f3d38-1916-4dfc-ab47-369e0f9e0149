const express = require('express');
const Joi = require('joi');
const { Customer, Session } = require('../models');
const { 
  authenticateToken, 
  requirePermission 
} = require('../middleware/auth');
const { validate, validateQuery, schemas, querySchemas } = require('../middleware/validation');
const { catchAsync, AppError, sendSuccess, sendPaginatedResponse } = require('../middleware/errorHandler');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// @desc    Get all customers
// @route   GET /api/customers
// @access  Private
router.get('/',
  validateQuery(Joi.object({
    ...querySchemas.pagination,
    membershipType: Joi.string().valid('regular', 'vip', 'premium').optional(),
    isActive: Joi.boolean().optional(),
    isBlacklisted: Joi.boolean().optional(),
    search: Joi.string().trim().optional()
  })),
  catchAsync(async (req, res, next) => {
    const { page, limit, sort, order, membershipType, isActive, isBlacklisted, search } = req.query;

    // Build query
    const query = {};
    if (membershipType) query.membershipType = membershipType;
    if (isActive !== undefined) query.isActive = isActive;
    if (isBlacklisted !== undefined) query.isBlacklisted = isBlacklisted;
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort object
    const sortObj = {};
    if (sort) {
      sortObj[sort] = order === 'asc' ? 1 : -1;
    } else {
      sortObj.name = 1; // Default sort by name
    }

    // Execute query with pagination
    const customers = await Customer.find(query)
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name')
      .sort(sortObj)
      .limit(limit)
      .skip((page - 1) * limit);

    const total = await Customer.countDocuments(query);

    sendPaginatedResponse(res, customers, page, limit, total, 'Customers retrieved successfully', 'تم استرداد العملاء بنجاح');
  })
);

// @desc    Get customer by ID
// @route   GET /api/customers/:id
// @access  Private
router.get('/:id',
  catchAsync(async (req, res, next) => {
    const customer = await Customer.findById(req.params.id)
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name');

    if (!customer) {
      return next(new AppError('Customer not found', 404, 'العميل غير موجود'));
    }

    sendSuccess(res, 200, { customer }, 'Customer retrieved successfully', 'تم استرداد العميل بنجاح');
  })
);

// @desc    Create new customer
// @route   POST /api/customers
// @access  Private (Requires canManageCustomers permission)
router.post('/',
  requirePermission('canManageCustomers'),
  validate(schemas.createCustomer),
  catchAsync(async (req, res, next) => {
    const customerData = {
      ...req.body,
      createdBy: req.user._id
    };

    // Check if customer with same phone or email already exists
    if (req.body.phone) {
      const existingCustomer = await Customer.findOne({ phone: req.body.phone, isActive: true });
      if (existingCustomer) {
        return next(new AppError('Customer with this phone number already exists', 400, 'عميل بهذا الرقم موجود بالفعل'));
      }
    }

    if (req.body.email) {
      const existingCustomer = await Customer.findOne({ email: req.body.email, isActive: true });
      if (existingCustomer) {
        return next(new AppError('Customer with this email already exists', 400, 'عميل بهذا البريد الإلكتروني موجود بالفعل'));
      }
    }

    const customer = await Customer.create(customerData);

    const populatedCustomer = await Customer.findById(customer._id)
      .populate('createdBy', 'name');

    sendSuccess(res, 201, { customer: populatedCustomer }, 'Customer created successfully', 'تم إنشاء العميل بنجاح');
  })
);

// @desc    Update customer
// @route   PUT /api/customers/:id
// @access  Private (Requires canManageCustomers permission)
router.put('/:id',
  requirePermission('canManageCustomers'),
  validate(schemas.updateCustomer),
  catchAsync(async (req, res, next) => {
    const customer = await Customer.findById(req.params.id);

    if (!customer) {
      return next(new AppError('Customer not found', 404, 'العميل غير موجود'));
    }

    // Check if phone or email is already taken by another customer
    if (req.body.phone && req.body.phone !== customer.phone) {
      const existingCustomer = await Customer.findOne({ 
        phone: req.body.phone, 
        _id: { $ne: req.params.id },
        isActive: true 
      });
      if (existingCustomer) {
        return next(new AppError('Phone number already in use', 400, 'رقم الهاتف مستخدم بالفعل'));
      }
    }

    if (req.body.email && req.body.email !== customer.email) {
      const existingCustomer = await Customer.findOne({ 
        email: req.body.email, 
        _id: { $ne: req.params.id },
        isActive: true 
      });
      if (existingCustomer) {
        return next(new AppError('Email already in use', 400, 'البريد الإلكتروني مستخدم بالفعل'));
      }
    }

    const updatedCustomer = await Customer.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedBy: req.user._id },
      { new: true, runValidators: true }
    ).populate('createdBy', 'name').populate('updatedBy', 'name');

    sendSuccess(res, 200, { customer: updatedCustomer }, 'Customer updated successfully', 'تم تحديث العميل بنجاح');
  })
);

// @desc    Delete customer (soft delete)
// @route   DELETE /api/customers/:id
// @access  Private (Requires canManageCustomers permission)
router.delete('/:id',
  requirePermission('canManageCustomers'),
  catchAsync(async (req, res, next) => {
    const customer = await Customer.findById(req.params.id);

    if (!customer) {
      return next(new AppError('Customer not found', 404, 'العميل غير موجود'));
    }

    // Check if customer has active sessions
    const activeSession = await Session.findOne({ 
      customer: customer._id, 
      status: { $in: ['active', 'paused'] } 
    });
    
    if (activeSession) {
      return next(new AppError('Cannot delete customer with active sessions', 400, 'لا يمكن حذف عميل له جلسات نشطة'));
    }

    // Soft delete by setting isActive to false
    await Customer.findByIdAndUpdate(req.params.id, { 
      isActive: false, 
      updatedBy: req.user._id 
    });

    sendSuccess(res, 200, null, 'Customer deleted successfully', 'تم حذف العميل بنجاح');
  })
);

// @desc    Get customer session history
// @route   GET /api/customers/:id/sessions
// @access  Private
router.get('/:id/sessions',
  validateQuery(querySchemas.pagination),
  catchAsync(async (req, res, next) => {
    const { page, limit } = req.query;
    const customerId = req.params.id;

    // Check if customer exists
    const customer = await Customer.findById(customerId);
    if (!customer) {
      return next(new AppError('Customer not found', 404, 'العميل غير موجود'));
    }

    // Get sessions for this customer
    const sessions = await Session.find({ customer: customerId })
      .populate('console', 'name consoleId type')
      .populate('createdBy', 'name')
      .populate('endedBy', 'name')
      .sort({ startTime: -1 })
      .limit(limit)
      .skip((page - 1) * limit);

    const total = await Session.countDocuments({ customer: customerId });

    sendPaginatedResponse(res, sessions, page, limit, total, 'Customer sessions retrieved', 'تم استرداد جلسات العميل');
  })
);

// @desc    Add prepaid balance
// @route   POST /api/customers/:id/prepaid
// @access  Private (Requires canManageCustomers permission)
router.post('/:id/prepaid',
  requirePermission('canManageCustomers'),
  catchAsync(async (req, res, next) => {
    const { amount } = req.body;

    if (!amount || amount <= 0) {
      return next(new AppError('Amount must be greater than 0', 400, 'المبلغ يجب أن يكون أكبر من صفر'));
    }

    const customer = await Customer.findById(req.params.id);

    if (!customer) {
      return next(new AppError('Customer not found', 404, 'العميل غير موجود'));
    }

    const newBalance = await customer.addPrepaidBalance(amount);

    sendSuccess(res, 200, { 
      customer: { 
        id: customer._id, 
        name: customer.name, 
        prepaidBalance: newBalance 
      } 
    }, 'Prepaid balance added successfully', 'تم إضافة الرصيد المدفوع مسبقاً بنجاح');
  })
);

// @desc    Get customer statistics
// @route   GET /api/customers/stats
// @access  Private
router.get('/analytics/stats',
  catchAsync(async (req, res, next) => {
    const stats = await Customer.getStatistics();

    sendSuccess(res, 200, { stats }, 'Customer statistics retrieved', 'تم استرداد إحصائيات العملاء');
  })
);

// @desc    Get top customers
// @route   GET /api/customers/top
// @access  Private
router.get('/analytics/top',
  validateQuery(Joi.object({
    limit: Joi.number().integer().min(1).max(50).default(10)
  })),
  catchAsync(async (req, res, next) => {
    const { limit } = req.query;

    const topCustomers = await Customer.getTopCustomers(limit);

    sendSuccess(res, 200, { customers: topCustomers }, 'Top customers retrieved', 'تم استرداد أفضل العملاء');
  })
);

// @desc    Blacklist/Unblacklist customer
// @route   PATCH /api/customers/:id/blacklist
// @access  Private (Requires canManageCustomers permission)
router.patch('/:id/blacklist',
  requirePermission('canManageCustomers'),
  catchAsync(async (req, res, next) => {
    const { isBlacklisted, reason } = req.body;

    if (typeof isBlacklisted !== 'boolean') {
      return next(new AppError('isBlacklisted must be a boolean', 400, 'isBlacklisted يجب أن يكون true أو false'));
    }

    const customer = await Customer.findById(req.params.id);

    if (!customer) {
      return next(new AppError('Customer not found', 404, 'العميل غير موجود'));
    }

    // If blacklisting, check for active sessions
    if (isBlacklisted) {
      const activeSession = await Session.findOne({ 
        customer: customer._id, 
        status: { $in: ['active', 'paused'] } 
      });
      
      if (activeSession) {
        return next(new AppError('Cannot blacklist customer with active sessions', 400, 'لا يمكن حظر عميل له جلسات نشطة'));
      }

      if (!reason) {
        return next(new AppError('Blacklist reason is required', 400, 'سبب الحظر مطلوب'));
      }
    }

    customer.isBlacklisted = isBlacklisted;
    customer.blacklistReason = isBlacklisted ? reason : null;
    customer.updatedBy = req.user._id;

    await customer.save();

    const action = isBlacklisted ? 'blacklisted' : 'unblacklisted';
    const actionAr = isBlacklisted ? 'محظور' : 'غير محظور';

    sendSuccess(res, 200, { customer }, `Customer ${action} successfully`, `تم ${actionAr} العميل بنجاح`);
  })
);

// @desc    Update customer membership
// @route   PATCH /api/customers/:id/membership
// @access  Private (Requires canManageCustomers permission)
router.patch('/:id/membership',
  requirePermission('canManageCustomers'),
  catchAsync(async (req, res, next) => {
    const { membershipType } = req.body;

    if (!['regular', 'vip', 'premium'].includes(membershipType)) {
      return next(new AppError('Invalid membership type', 400, 'نوع العضوية غير صحيح'));
    }

    const customer = await Customer.findById(req.params.id);

    if (!customer) {
      return next(new AppError('Customer not found', 404, 'العميل غير موجود'));
    }

    customer.membershipType = membershipType;
    customer.updatedBy = req.user._id;

    await customer.save();

    sendSuccess(res, 200, { customer }, 'Customer membership updated successfully', 'تم تحديث عضوية العميل بنجاح');
  })
);

// @desc    Search customers
// @route   GET /api/customers/search
// @access  Private
router.get('/search/query',
  validateQuery(Joi.object({
    q: Joi.string().trim().min(1).required().messages({
      'string.empty': 'Search query is required',
      'any.required': 'Search query is required'
    }),
    limit: Joi.number().integer().min(1).max(50).default(10)
  })),
  catchAsync(async (req, res, next) => {
    const { q, limit } = req.query;

    const customers = await Customer.find({
      $and: [
        { isActive: true },
        { isBlacklisted: false },
        {
          $or: [
            { name: { $regex: q, $options: 'i' } },
            { phone: { $regex: q, $options: 'i' } },
            { email: { $regex: q, $options: 'i' } }
          ]
        }
      ]
    })
    .select('name phone email membershipType prepaidBalance')
    .limit(limit)
    .sort({ name: 1 });

    sendSuccess(res, 200, { customers }, 'Customers found', 'تم العثور على العملاء');
  })
);

module.exports = router;
