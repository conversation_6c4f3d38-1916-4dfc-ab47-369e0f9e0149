import { useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { 
  EyeIcon, 
  EyeSlashIcon, 
  EnvelopeIcon, 
  LockClosedIcon 
} from '@heroicons/react/24/outline'
import { useAuthStore } from '../../store/authStore'
import { LoadingButton } from '../../components/UI/LoadingSpinner'
import { validationUtils } from '../../utils/helpers'

const LoginPage = () => {
  const [showPassword, setShowPassword] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  
  const { login, isLoading } = useAuthStore()
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError
  } = useForm({
    defaultValues: {
      email: '',
      password: ''
    }
  })

  const from = location.state?.from?.pathname || '/dashboard'

  const onSubmit = async (data) => {
    try {
      const result = await login(data)
      
      if (result.success) {
        navigate(from, { replace: true })
      } else {
        setError('root', {
          type: 'manual',
          message: result.error
        })
      }
    } catch (error) {
      setError('root', {
        type: 'manual',
        message: 'حدث خطأ غير متوقع'
      })
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <motion.h2
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="text-2xl font-bold text-gray-900 mb-2"
        >
          تسجيل الدخول
        </motion.h2>
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="text-gray-600"
        >
          أدخل بياناتك للوصول إلى النظام
        </motion.p>
      </div>

      {/* Login Form */}
      <motion.form
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        onSubmit={handleSubmit(onSubmit)}
        className="space-y-6"
      >
        {/* Email Field */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
            البريد الإلكتروني
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <EnvelopeIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              id="email"
              type="email"
              autoComplete="email"
              className={`
                input-primary pr-10
                ${errors.email ? 'input-error' : ''}
              `}
              placeholder="أدخل البريد الإلكتروني"
              {...register('email', {
                required: 'البريد الإلكتروني مطلوب',
                validate: (value) => 
                  validationUtils.email(value) || 'البريد الإلكتروني غير صحيح'
              })}
            />
          </div>
          {errors.email && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-1 text-sm text-danger-600"
            >
              {errors.email.message}
            </motion.p>
          )}
        </div>

        {/* Password Field */}
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
            كلمة المرور
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <LockClosedIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              autoComplete="current-password"
              className={`
                input-primary pr-10 pl-10
                ${errors.password ? 'input-error' : ''}
              `}
              placeholder="أدخل كلمة المرور"
              {...register('password', {
                required: 'كلمة المرور مطلوبة',
                minLength: {
                  value: 6,
                  message: 'كلمة المرور يجب أن تكون على الأقل 6 أحرف'
                }
              })}
            />
            <button
              type="button"
              className="absolute inset-y-0 left-0 pl-3 flex items-center"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeSlashIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              ) : (
                <EyeIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              )}
            </button>
          </div>
          {errors.password && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-1 text-sm text-danger-600"
            >
              {errors.password.message}
            </motion.p>
          )}
        </div>

        {/* Remember Me */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input
              id="remember-me"
              name="remember-me"
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label htmlFor="remember-me" className="mr-2 block text-sm text-gray-700">
              تذكرني
            </label>
          </div>
          
          <div className="text-sm">
            <button
              type="button"
              className="font-medium text-primary-600 hover:text-primary-500 transition-colors"
            >
              نسيت كلمة المرور؟
            </button>
          </div>
        </div>

        {/* Error Message */}
        {errors.root && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-danger-50 border border-danger-200 rounded-lg p-3"
          >
            <p className="text-sm text-danger-600 text-center">
              {errors.root.message}
            </p>
          </motion.div>
        )}

        {/* Submit Button */}
        <LoadingButton
          type="submit"
          loading={isLoading}
          className="w-full btn-primary py-3 text-base font-medium"
        >
          تسجيل الدخول
        </LoadingButton>
      </motion.form>

      {/* Demo Credentials */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="bg-gray-50 rounded-lg p-4 border border-gray-200"
      >
        <h3 className="text-sm font-medium text-gray-700 mb-2">بيانات تجريبية:</h3>
        <div className="text-xs text-gray-600 space-y-1">
          <p><strong>مدير:</strong> <EMAIL> / admin123456</p>
          <p><strong>موظف:</strong> <EMAIL> / staff123456</p>
        </div>
      </motion.div>

      {/* Features */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
        className="text-center text-sm text-gray-500"
      >
        <div className="flex items-center justify-center space-x-4 space-x-reverse">
          <div className="flex items-center">
            <div className="w-2 h-2 bg-success-500 rounded-full ml-2"></div>
            <span>إدارة الجلسات</span>
          </div>
          <div className="flex items-center">
            <div className="w-2 h-2 bg-primary-500 rounded-full ml-2"></div>
            <span>تتبع الإيرادات</span>
          </div>
          <div className="flex items-center">
            <div className="w-2 h-2 bg-warning-500 rounded-full ml-2"></div>
            <span>إدارة العملاء</span>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default LoginPage
