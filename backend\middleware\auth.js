const jwt = require('jsonwebtoken');
const { User } = require('../models');

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

// Verify JWT token middleware
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Access token required',
        message_ar: 'رمز الوصول مطلوب'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-super-secret-jwt-key');
    
    // Get user from database
    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid token - user not found',
        message_ar: 'رمز غير صحيح - المستخدم غير موجود'
      });
    }

    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        error: 'Account is deactivated',
        message_ar: 'الحساب معطل'
      });
    }

    // Add user to request object
    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: 'Invalid token',
        message_ar: 'رمز غير صحيح'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: 'Token expired',
        message_ar: 'انتهت صلاحية الرمز'
      });
    }

    console.error('Authentication error:', error);
    res.status(500).json({
      success: false,
      error: 'Authentication failed',
      message_ar: 'فشل في المصادقة'
    });
  }
};

// Check if user is admin
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required',
      message_ar: 'يتطلب صلاحيات المدير'
    });
  }
  next();
};

// Check if user is admin or staff
const requireStaff = (req, res, next) => {
  if (!['admin', 'staff'].includes(req.user.role)) {
    return res.status(403).json({
      success: false,
      error: 'Staff access required',
      message_ar: 'يتطلب صلاحيات الموظف'
    });
  }
  next();
};

// Check specific permission
const requirePermission = (permission) => {
  return (req, res, next) => {
    const userPermissions = req.user.fullPermissions;
    
    if (!userPermissions[permission]) {
      return res.status(403).json({
        success: false,
        error: `Permission required: ${permission}`,
        message_ar: `صلاحية مطلوبة: ${permission}`
      });
    }
    
    next();
  };
};

// Rate limiting for login attempts
const loginAttempts = new Map();

const rateLimitLogin = (req, res, next) => {
  const ip = req.ip || req.connection.remoteAddress;
  const now = Date.now();
  const windowMs = 15 * 60 * 1000; // 15 minutes
  const maxAttempts = 5;

  if (!loginAttempts.has(ip)) {
    loginAttempts.set(ip, { count: 1, resetTime: now + windowMs });
    return next();
  }

  const attempts = loginAttempts.get(ip);

  if (now > attempts.resetTime) {
    // Reset the counter
    loginAttempts.set(ip, { count: 1, resetTime: now + windowMs });
    return next();
  }

  if (attempts.count >= maxAttempts) {
    return res.status(429).json({
      success: false,
      error: 'Too many login attempts. Please try again later.',
      message_ar: 'عدد كبير من محاولات تسجيل الدخول. يرجى المحاولة لاحقاً.',
      retryAfter: Math.ceil((attempts.resetTime - now) / 1000)
    });
  }

  attempts.count++;
  next();
};

// Clear login attempts on successful login
const clearLoginAttempts = (ip) => {
  loginAttempts.delete(ip);
};

// Middleware to log user activity
const logActivity = (action) => {
  return (req, res, next) => {
    // Store activity info in request for later logging
    req.activityLog = {
      userId: req.user ? req.user._id : null,
      action,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    };
    next();
  };
};

// Optional middleware to check if user session is still valid
const checkSessionValidity = async (req, res, next) => {
  try {
    if (!req.user) return next();

    // Check if user's last login is within session timeout
    const sessionTimeout = 30 * 60 * 1000; // 30 minutes in milliseconds
    const lastActivity = req.user.lastLogin || req.user.updatedAt;
    const now = new Date();

    if (now - lastActivity > sessionTimeout) {
      return res.status(401).json({
        success: false,
        error: 'Session expired due to inactivity',
        message_ar: 'انتهت الجلسة بسبب عدم النشاط'
      });
    }

    // Update last activity
    await User.findByIdAndUpdate(req.user._id, { lastLogin: now });
    next();
  } catch (error) {
    console.error('Session validity check error:', error);
    next(); // Continue even if session check fails
  }
};

module.exports = {
  generateToken,
  authenticateToken,
  requireAdmin,
  requireStaff,
  requirePermission,
  rateLimitLogin,
  clearLoginAttempts,
  logActivity,
  checkSessionValidity
};
