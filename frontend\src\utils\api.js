import axios from 'axios'
import toast from 'react-hot-toast'

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add timestamp to prevent caching
    config.params = {
      ...config.params,
      _t: Date.now()
    }

    // Add auth token if available
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // Log request in development
    if (import.meta.env.DEV) {
      console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, {
        data: config.data,
        params: config.params
      })
    }

    return config
  },
  (error) => {
    console.error('Request interceptor error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    // Log response in development
    if (import.meta.env.DEV) {
      console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data
      })
    }

    return response
  },
  (error) => {
    // Log error in development
    if (import.meta.env.DEV) {
      console.error(`❌ ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      })
    }

    // Handle different error types
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response

      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          if (window.location.pathname !== '/login') {
            localStorage.removeItem('token')
            delete api.defaults.headers.common['Authorization']
            window.location.href = '/login'
            toast.error(data.message_ar || 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى')
          }
          break

        case 403:
          // Forbidden
          toast.error(data.message_ar || 'ليس لديك صلاحية للوصول لهذا المورد')
          break

        case 404:
          // Not found
          toast.error(data.message_ar || 'المورد المطلوب غير موجود')
          break

        case 422:
          // Validation error
          if (data.errors && Array.isArray(data.errors)) {
            data.errors.forEach(err => {
              toast.error(err.message)
            })
          } else {
            toast.error(data.message_ar || 'خطأ في البيانات المدخلة')
          }
          break

        case 429:
          // Rate limit exceeded
          toast.error(data.message_ar || 'عدد كبير من الطلبات، يرجى المحاولة لاحقاً')
          break

        case 500:
          // Server error
          toast.error(data.message_ar || 'خطأ في الخادم، يرجى المحاولة لاحقاً')
          break

        default:
          // Other errors
          toast.error(data.message_ar || data.message || 'حدث خطأ غير متوقع')
      }
    } else if (error.request) {
      // Network error
      toast.error('خطأ في الاتصال بالخادم، يرجى التحقق من الاتصال بالإنترنت')
    } else {
      // Other error
      toast.error('حدث خطأ غير متوقع')
    }

    return Promise.reject(error)
  }
)

// API helper functions
export const apiHelpers = {
  // Generic CRUD operations
  get: (url, config = {}) => api.get(url, config),
  post: (url, data = {}, config = {}) => api.post(url, data, config),
  put: (url, data = {}, config = {}) => api.put(url, data, config),
  patch: (url, data = {}, config = {}) => api.patch(url, data, config),
  delete: (url, config = {}) => api.delete(url, config),

  // Paginated requests
  getPaginated: async (url, params = {}) => {
    const response = await api.get(url, { params })
    return {
      data: response.data.data,
      pagination: response.data.pagination,
      success: response.data.success,
      message: response.data.message_ar || response.data.message
    }
  },

  // Upload file
  uploadFile: (url, file, onProgress = null) => {
    const formData = new FormData()
    formData.append('file', file)

    return api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          onProgress(percentCompleted)
        }
      }
    })
  },

  // Download file
  downloadFile: async (url, filename = null) => {
    try {
      const response = await api.get(url, {
        responseType: 'blob'
      })

      // Create blob link to download
      const blob = new Blob([response.data])
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      
      // Get filename from response headers or use provided filename
      const contentDisposition = response.headers['content-disposition']
      if (contentDisposition && !filename) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/)
        if (filenameMatch) {
          filename = filenameMatch[1]
        }
      }
      
      link.download = filename || 'download'
      link.click()
      
      // Cleanup
      window.URL.revokeObjectURL(link.href)
      
      return { success: true }
    } catch (error) {
      console.error('Download error:', error)
      return { success: false, error: error.message }
    }
  },

  // Batch requests
  batch: async (requests) => {
    try {
      const responses = await Promise.allSettled(requests)
      return responses.map((response, index) => ({
        index,
        success: response.status === 'fulfilled',
        data: response.status === 'fulfilled' ? response.value.data : null,
        error: response.status === 'rejected' ? response.reason : null
      }))
    } catch (error) {
      console.error('Batch request error:', error)
      throw error
    }
  },

  // Retry request
  retry: async (requestFn, maxRetries = 3, delay = 1000) => {
    let lastError
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await requestFn()
      } catch (error) {
        lastError = error
        
        // Don't retry on client errors (4xx)
        if (error.response?.status >= 400 && error.response?.status < 500) {
          throw error
        }
        
        // Wait before retrying
        if (i < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
        }
      }
    }
    
    throw lastError
  }
}

// Export default api instance
export default api
