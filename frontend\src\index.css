@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Arabic Font Imports */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

/* Base RTL Styles */
* {
  font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
}

html {
  direction: rtl;
  text-align: right;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
  color: #1f2937;
  font-feature-settings: 'kern' 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Glassmorphism Effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

/* Card Hover Effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Button Animations */
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 active:scale-95;
}

.btn-success {
  @apply bg-success-600 hover:bg-success-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl;
}

.btn-danger {
  @apply bg-danger-600 hover:bg-danger-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl;
}

.btn-warning {
  @apply bg-warning-600 hover:bg-warning-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl;
}

/* Input Styles */
.input-primary {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 bg-white shadow-sm;
}

.input-error {
  @apply border-danger-500 focus:ring-danger-500 focus:border-danger-500;
}

/* Loading Spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pulse Animation */
.pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide In Animations */
.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Status Indicators */
.status-available {
  @apply bg-success-100 text-success-800 border border-success-200;
}

.status-active {
  @apply bg-primary-100 text-primary-800 border border-primary-200;
}

.status-maintenance {
  @apply bg-warning-100 text-warning-800 border border-warning-200;
}

.status-offline {
  @apply bg-gray-100 text-gray-800 border border-gray-200;
}

.status-completed {
  @apply bg-success-100 text-success-800 border border-success-200;
}

.status-cancelled {
  @apply bg-danger-100 text-danger-800 border border-danger-200;
}

.status-paused {
  @apply bg-warning-100 text-warning-800 border border-warning-200;
}

/* Console Type Colors */
.console-ps5 {
  @apply bg-blue-100 text-blue-800 border border-blue-200;
}

.console-ps4 {
  @apply bg-indigo-100 text-indigo-800 border border-indigo-200;
}

.console-xbox {
  @apply bg-green-100 text-green-800 border border-green-200;
}

.console-pc {
  @apply bg-purple-100 text-purple-800 border border-purple-200;
}

.console-nintendo {
  @apply bg-red-100 text-red-800 border border-red-200;
}

/* Membership Type Colors */
.membership-regular {
  @apply bg-gray-100 text-gray-800 border border-gray-200;
}

.membership-vip {
  @apply bg-yellow-100 text-yellow-800 border border-yellow-200;
}

.membership-premium {
  @apply bg-purple-100 text-purple-800 border border-purple-200;
}

/* Custom Utilities */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .glass, .glass-dark {
    background: white !important;
    backdrop-filter: none !important;
    border: 1px solid #e5e7eb !important;
    box-shadow: none !important;
  }
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
  .card-hover:hover {
    transform: none;
  }
  
  .btn-primary, .btn-secondary, .btn-success, .btn-danger, .btn-warning {
    transform: none !important;
  }
  
  .btn-primary:hover, .btn-secondary:hover, .btn-success:hover, .btn-danger:hover, .btn-warning:hover {
    transform: none !important;
  }
}

/* Focus Styles for Accessibility */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .glass, .glass-dark {
    background: white;
    border: 2px solid black;
    backdrop-filter: none;
  }
}
