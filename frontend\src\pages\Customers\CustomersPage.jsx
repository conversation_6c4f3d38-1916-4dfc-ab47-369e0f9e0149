import { motion } from 'framer-motion'
import { UserGroupIcon } from '@heroicons/react/24/outline'

const CustomersPage = () => {
  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-sm p-8 border border-gray-200 text-center"
      >
        <UserGroupIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h1 className="text-2xl font-bold text-gray-900 mb-2">إدارة العملاء</h1>
        <p className="text-gray-600 mb-6">
          هذه الصفحة ستحتوي على إدارة بيانات العملاء والعضويات والرصيد المدفوع مسبقاً
        </p>
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <p className="text-purple-800 text-sm">
            🚧 هذه الصفحة قيد التطوير وستكون متاحة قريباً
          </p>
        </div>
      </motion.div>
    </div>
  )
}

export default CustomersPage
