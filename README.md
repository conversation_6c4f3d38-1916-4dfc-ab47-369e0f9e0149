# PlayStation Shop Management System
## نظام إدارة محل البلايستيشن

A complete web application for managing PlayStation gaming shops with Arabic localization and RTL support.

## 🧩 Tech Stack
- **Frontend**: React + Vite + Tailwind CSS + Zustand
- **Backend**: Node.js + Express + MongoDB
- **Authentication**: JWT
- **UI/UX**: Framer Motion + Glassmorphism Design
- **Localization**: Arabic (RTL)

## 🎯 Features
- Real-time session management
- Console monitoring and control
- Customer management
- Revenue tracking and reports
- Admin/Staff authentication
- Arabic UI with RTL support

## 📁 Project Structure
```
├── frontend/          # React frontend application
├── backend/           # Node.js backend API
├── docs/             # Documentation
└── README.md         # This file
```

## 🚀 Quick Start

### Backend Setup
```bash
cd backend
npm install
npm run dev
```

### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

## 🌍 Localization
- Full Arabic UI support
- RTL (Right-to-Left) layout
- Egyptian Pound (EGP) currency
- 24-hour time format

## 📝 License
MIT License
