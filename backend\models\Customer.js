const mongoose = require('mongoose');

const customerSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'اسم العميل مطلوب'],
    trim: true,
    maxlength: [100, 'اسم العميل يجب أن يكون أقل من 100 حرف']
  },
  phone: {
    type: String,
    trim: true,
    match: [/^[\+]?[0-9\-\(\)\s]+$/, 'رقم الهاتف غير صحيح'],
    maxlength: [20, 'رقم الهاتف يجب أن يكون أقل من 20 رقم']
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'البريد الإلكتروني غير صحيح']
  },
  dateOfBirth: {
    type: Date,
    validate: {
      validator: function(value) {
        return !value || value < new Date();
      },
      message: 'تاريخ الميلاد يجب أن يكون في الماضي'
    }
  },
  membershipType: {
    type: String,
    enum: {
      values: ['regular', 'vip', 'premium'],
      message: 'نوع العضوية غير صحيح'
    },
    default: 'regular'
  },
  prepaidBalance: {
    type: Number,
    default: 0,
    min: [0, 'الرصيد المدفوع مسبقاً لا يمكن أن يكون سالب'],
    validate: {
      validator: function(value) {
        return Number.isFinite(value);
      },
      message: 'الرصيد المدفوع مسبقاً يجب أن يكون رقم صحيح'
    }
  },
  loyaltyPoints: {
    type: Number,
    default: 0,
    min: [0, 'نقاط الولاء لا يمكن أن تكون سالبة']
  },
  preferences: {
    favoriteConsoleType: {
      type: String,
      enum: ['PS5', 'PS4', 'PS4_PRO', 'XBOX_SERIES_X', 'XBOX_SERIES_S', 'XBOX_ONE', 'PC', 'NINTENDO_SWITCH']
    },
    preferredTimeSlots: [{
      type: String,
      enum: ['morning', 'afternoon', 'evening', 'night']
    }],
    notifications: {
      sms: { type: Boolean, default: false },
      email: { type: Boolean, default: false },
      whatsapp: { type: Boolean, default: false }
    }
  },
  statistics: {
    totalSessions: { type: Number, default: 0 },
    totalHoursPlayed: { type: Number, default: 0 },
    totalAmountSpent: { type: Number, default: 0 },
    averageSessionDuration: { type: Number, default: 0 },
    lastVisit: { type: Date },
    firstVisit: { type: Date },
    favoriteGames: [{ type: String, trim: true }]
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'الملاحظات يجب أن تكون أقل من 1000 حرف']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isBlacklisted: {
    type: Boolean,
    default: false
  },
  blacklistReason: {
    type: String,
    trim: true,
    maxlength: [500, 'سبب الحظر يجب أن يكون أقل من 500 حرف']
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
customerSchema.index({ name: 1 });
customerSchema.index({ phone: 1 });
customerSchema.index({ email: 1 });
customerSchema.index({ membershipType: 1 });
customerSchema.index({ isActive: 1 });
customerSchema.index({ isBlacklisted: 1 });
customerSchema.index({ 'statistics.totalAmountSpent': -1 });
customerSchema.index({ 'statistics.lastVisit': -1 });

// Virtual for age calculation
customerSchema.virtual('age').get(function() {
  if (!this.dateOfBirth) return null;
  const today = new Date();
  const birthDate = new Date(this.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
});

// Virtual for Arabic membership type
customerSchema.virtual('membershipTypeArabic').get(function() {
  const membershipMap = {
    'regular': 'عادي',
    'vip': 'مميز',
    'premium': 'بريميوم'
  };
  return membershipMap[this.membershipType] || this.membershipType;
});

// Virtual for customer sessions
customerSchema.virtual('sessions', {
  ref: 'Session',
  localField: '_id',
  foreignField: 'customer'
});

// Method to update statistics after session
customerSchema.methods.updateStatistics = async function(sessionData) {
  this.statistics.totalSessions += 1;
  this.statistics.totalHoursPlayed += sessionData.duration;
  this.statistics.totalAmountSpent += sessionData.totalCost;
  this.statistics.lastVisit = new Date();
  
  // Set first visit if not set
  if (!this.statistics.firstVisit) {
    this.statistics.firstVisit = new Date();
  }
  
  // Calculate average session duration
  this.statistics.averageSessionDuration = this.statistics.totalHoursPlayed / this.statistics.totalSessions;
  
  // Add loyalty points (1 point per 10 EGP spent)
  this.loyaltyPoints += Math.floor(sessionData.totalCost / 10);
  
  await this.save();
};

// Method to deduct from prepaid balance
customerSchema.methods.deductPrepaidBalance = async function(amount) {
  if (this.prepaidBalance < amount) {
    throw new Error('الرصيد المدفوع مسبقاً غير كافي');
  }
  this.prepaidBalance -= amount;
  await this.save();
  return this.prepaidBalance;
};

// Method to add to prepaid balance
customerSchema.methods.addPrepaidBalance = async function(amount) {
  this.prepaidBalance += amount;
  await this.save();
  return this.prepaidBalance;
};

// Static method to get customer statistics
customerSchema.statics.getStatistics = async function() {
  const stats = await this.aggregate([
    { $match: { isActive: true, isBlacklisted: false } },
    {
      $group: {
        _id: null,
        totalCustomers: { $sum: 1 },
        regularCustomers: { 
          $sum: { 
            $cond: [{ $eq: ['$membershipType', 'regular'] }, 1, 0] 
          } 
        },
        vipCustomers: { 
          $sum: { 
            $cond: [{ $eq: ['$membershipType', 'vip'] }, 1, 0] 
          } 
        },
        premiumCustomers: { 
          $sum: { 
            $cond: [{ $eq: ['$membershipType', 'premium'] }, 1, 0] 
          } 
        },
        totalPrepaidBalance: { $sum: '$prepaidBalance' },
        totalLoyaltyPoints: { $sum: '$loyaltyPoints' },
        averageSpent: { $avg: '$statistics.totalAmountSpent' }
      }
    }
  ]);
  
  return stats[0] || {
    totalCustomers: 0,
    regularCustomers: 0,
    vipCustomers: 0,
    premiumCustomers: 0,
    totalPrepaidBalance: 0,
    totalLoyaltyPoints: 0,
    averageSpent: 0
  };
};

// Static method to find top customers
customerSchema.statics.getTopCustomers = function(limit = 10) {
  return this.find({ isActive: true, isBlacklisted: false })
    .sort({ 'statistics.totalAmountSpent': -1 })
    .limit(limit)
    .select('name phone membershipType statistics prepaidBalance loyaltyPoints');
};

module.exports = mongoose.model('Customer', customerSchema);
