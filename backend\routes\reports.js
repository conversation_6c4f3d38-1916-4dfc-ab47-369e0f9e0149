const express = require('express');
const Joi = require('joi');
const { Report, Session, Console, Customer } = require('../models');
const { 
  authenticateToken, 
  requirePermission 
} = require('../middleware/auth');
const { validate, validateQuery, schemas, querySchemas } = require('../middleware/validation');
const { catchAsync, AppError, sendSuccess, sendPaginatedResponse } = require('../middleware/errorHandler');
const { dateUtils, numberUtils, arrayUtils } = require('../utils/helpers');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// @desc    Get all reports
// @route   GET /api/reports
// @access  Private (Requires canViewReports permission)
router.get('/',
  requirePermission('canViewReports'),
  validateQuery(Joi.object({
    ...querySchemas.pagination,
    type: Joi.string().valid('daily', 'weekly', 'monthly', 'yearly', 'custom', 'console_performance', 'customer_analysis', 'revenue_analysis').optional(),
    status: Joi.string().valid('generating', 'completed', 'failed').optional(),
    generatedBy: Joi.string().optional()
  })),
  catchAsync(async (req, res, next) => {
    const { page, limit, sort, order, type, status, generatedBy } = req.query;

    // Build query
    const query = {};
    if (type) query.type = type;
    if (status) query.status = status;
    if (generatedBy) query.generatedBy = generatedBy;

    // Build sort object
    const sortObj = {};
    if (sort) {
      sortObj[sort] = order === 'asc' ? 1 : -1;
    } else {
      sortObj.generatedAt = -1; // Default sort by generation date (newest first)
    }

    // Execute query with pagination
    const reports = await Report.find(query)
      .populate('generatedBy', 'name')
      .sort(sortObj)
      .limit(limit)
      .skip((page - 1) * limit);

    const total = await Report.countDocuments(query);

    sendPaginatedResponse(res, reports, page, limit, total, 'Reports retrieved successfully', 'تم استرداد التقارير بنجاح');
  })
);

// @desc    Get report by ID
// @route   GET /api/reports/:id
// @access  Private (Requires canViewReports permission)
router.get('/:id',
  requirePermission('canViewReports'),
  catchAsync(async (req, res, next) => {
    const report = await Report.findById(req.params.id)
      .populate('generatedBy', 'name')
      .populate('filters.consoles', 'name consoleId type')
      .populate('filters.customers', 'name phone membershipType');

    if (!report) {
      return next(new AppError('Report not found', 404, 'التقرير غير موجود'));
    }

    // Increment download count if report is completed
    if (report.status === 'completed') {
      await report.incrementDownload();
    }

    sendSuccess(res, 200, { report }, 'Report retrieved successfully', 'تم استرداد التقرير بنجاح');
  })
);

// @desc    Generate new report
// @route   POST /api/reports
// @access  Private (Requires canViewReports permission)
router.post('/',
  requirePermission('canViewReports'),
  validate(schemas.generateReport),
  catchAsync(async (req, res, next) => {
    const { title, type, startDate, endDate, format, filters } = req.body;

    // Create report record
    const report = await Report.create({
      title,
      type,
      dateRange: { startDate, endDate },
      format,
      filters: filters || {},
      generatedBy: req.user._id,
      status: 'generating'
    });

    // Generate report data asynchronously
    try {
      const reportData = await generateReportData(type, startDate, endDate, filters);
      
      // Update report with generated data
      report.data = reportData;
      report.status = 'completed';
      await report.save();

      const populatedReport = await Report.findById(report._id)
        .populate('generatedBy', 'name');

      sendSuccess(res, 201, { report: populatedReport }, 'Report generated successfully', 'تم إنشاء التقرير بنجاح');
    } catch (error) {
      // Mark report as failed
      await report.markFailed(error.message);
      return next(new AppError('Failed to generate report', 500, 'فشل في إنشاء التقرير'));
    }
  })
);

// @desc    Delete report
// @route   DELETE /api/reports/:id
// @access  Private (Requires canViewReports permission)
router.delete('/:id',
  requirePermission('canViewReports'),
  catchAsync(async (req, res, next) => {
    const report = await Report.findById(req.params.id);

    if (!report) {
      return next(new AppError('Report not found', 404, 'التقرير غير موجود'));
    }

    // Only allow deletion of own reports or if user is admin
    if (report.generatedBy.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return next(new AppError('Not authorized to delete this report', 403, 'غير مخول لحذف هذا التقرير'));
    }

    await Report.findByIdAndDelete(req.params.id);

    sendSuccess(res, 200, null, 'Report deleted successfully', 'تم حذف التقرير بنجاح');
  })
);

// @desc    Get recent reports
// @route   GET /api/reports/recent
// @access  Private (Requires canViewReports permission)
router.get('/analytics/recent',
  requirePermission('canViewReports'),
  validateQuery(Joi.object({
    limit: Joi.number().integer().min(1).max(50).default(10)
  })),
  catchAsync(async (req, res, next) => {
    const { limit } = req.query;

    const reports = await Report.getRecentReports(limit);

    sendSuccess(res, 200, { reports }, 'Recent reports retrieved', 'تم استرداد التقارير الحديثة');
  })
);

// @desc    Get dashboard analytics
// @route   GET /api/reports/dashboard
// @access  Private
router.get('/analytics/dashboard',
  catchAsync(async (req, res, next) => {
    const today = new Date();
    const startOfDay = dateUtils.getStartOfDay(today);
    const endOfDay = dateUtils.getEndOfDay(today);

    // Get today's session statistics
    const todayStats = await Session.getDailyStats(today);

    // Get console statistics
    const consoleStats = await Console.getStatistics();

    // Get customer statistics
    const customerStats = await Customer.getStatistics();

    // Get active sessions
    const activeSessions = await Session.getActiveSessions();

    // Calculate additional metrics
    const dashboardData = {
      today: {
        ...todayStats,
        date: dateUtils.formatDateArabic(today)
      },
      consoles: consoleStats,
      customers: customerStats,
      activeSessions: {
        count: activeSessions.length,
        sessions: activeSessions.slice(0, 5) // Latest 5 active sessions
      },
      summary: {
        totalRevenue: consoleStats.totalRevenue,
        totalHours: consoleStats.totalHours,
        totalCustomers: customerStats.totalCustomers,
        totalConsoles: consoleStats.totalConsoles,
        utilizationRate: consoleStats.totalConsoles > 0 
          ? numberUtils.calculatePercentage(consoleStats.activeConsoles, consoleStats.totalConsoles)
          : 0
      }
    };

    sendSuccess(res, 200, dashboardData, 'Dashboard analytics retrieved', 'تم استرداد تحليلات لوحة التحكم');
  })
);

// @desc    Get revenue analytics
// @route   GET /api/reports/revenue
// @access  Private (Requires canViewReports permission)
router.get('/analytics/revenue',
  requirePermission('canViewReports'),
  validateQuery(Joi.object({
    period: Joi.string().valid('week', 'month', 'year').default('month'),
    startDate: Joi.date().optional(),
    endDate: Joi.date().min(Joi.ref('startDate')).optional()
  })),
  catchAsync(async (req, res, next) => {
    const { period, startDate, endDate } = req.query;

    let queryStartDate, queryEndDate;

    if (startDate && endDate) {
      queryStartDate = new Date(startDate);
      queryEndDate = new Date(endDate);
    } else {
      const now = new Date();
      switch (period) {
        case 'week':
          queryStartDate = dateUtils.getStartOfWeek(now);
          queryEndDate = dateUtils.getEndOfWeek(now);
          break;
        case 'month':
          queryStartDate = dateUtils.getStartOfMonth(now);
          queryEndDate = dateUtils.getEndOfMonth(now);
          break;
        case 'year':
          queryStartDate = dateUtils.getStartOfYear(now);
          queryEndDate = dateUtils.getEndOfYear(now);
          break;
        default:
          queryStartDate = dateUtils.getStartOfMonth(now);
          queryEndDate = dateUtils.getEndOfMonth(now);
      }
    }

    // Get revenue data
    const revenueData = await Session.aggregate([
      {
        $match: {
          startTime: { $gte: queryStartDate, $lte: queryEndDate },
          status: 'completed',
          paymentStatus: 'paid'
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: "%Y-%m-%d", date: "$startTime" } },
            paymentMethod: "$paymentMethod"
          },
          totalRevenue: { $sum: "$finalAmount" },
          sessionCount: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: "$_id.date",
          totalRevenue: { $sum: "$totalRevenue" },
          sessionCount: { $sum: "$sessionCount" },
          paymentMethods: {
            $push: {
              method: "$_id.paymentMethod",
              revenue: "$totalRevenue",
              count: "$sessionCount"
            }
          }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Calculate totals
    const totals = revenueData.reduce((acc, day) => {
      acc.totalRevenue += day.totalRevenue;
      acc.totalSessions += day.sessionCount;
      return acc;
    }, { totalRevenue: 0, totalSessions: 0 });

    sendSuccess(res, 200, {
      period,
      startDate: queryStartDate,
      endDate: queryEndDate,
      dailyData: revenueData,
      totals
    }, 'Revenue analytics retrieved', 'تم استرداد تحليلات الإيرادات');
  })
);

// Helper function to generate report data
async function generateReportData(type, startDate, endDate, filters = {}) {
  const start = new Date(startDate);
  const end = new Date(endDate);

  // Base query for sessions
  const sessionQuery = {
    startTime: { $gte: start, $lte: end }
  };

  // Apply filters
  if (filters.consoles && filters.consoles.length > 0) {
    sessionQuery.console = { $in: filters.consoles };
  }
  if (filters.customers && filters.customers.length > 0) {
    sessionQuery.customer = { $in: filters.customers };
  }
  if (filters.paymentMethods && filters.paymentMethods.length > 0) {
    sessionQuery.paymentMethod = { $in: filters.paymentMethods };
  }
  if (filters.sessionStatus && filters.sessionStatus.length > 0) {
    sessionQuery.status = { $in: filters.sessionStatus };
  }

  // Get sessions data
  const sessions = await Session.find(sessionQuery)
    .populate('console', 'name type')
    .populate('customer', 'name membershipType');

  // Calculate revenue statistics
  const revenue = {
    total: 0,
    cash: 0,
    prepaid: 0,
    card: 0,
    mixed: 0,
    discounts: 0,
    taxes: 0
  };

  sessions.forEach(session => {
    if (session.status === 'completed' && session.paymentStatus === 'paid') {
      revenue.total += session.finalAmount || 0;
      revenue[session.paymentMethod] += session.finalAmount || 0;
      revenue.discounts += session.discountAmount || 0;
      revenue.taxes += session.taxAmount || 0;
    }
  });

  // Calculate session statistics
  const sessionStats = {
    total: sessions.length,
    completed: sessions.filter(s => s.status === 'completed').length,
    active: sessions.filter(s => s.status === 'active').length,
    cancelled: sessions.filter(s => s.status === 'cancelled').length,
    totalHours: sessions.reduce((sum, s) => sum + (s.actualDuration || 0), 0) / 60,
    averageDuration: 0
  };

  if (sessionStats.completed > 0) {
    const completedSessions = sessions.filter(s => s.status === 'completed');
    sessionStats.averageDuration = completedSessions.reduce((sum, s) => sum + (s.actualDuration || 0), 0) / completedSessions.length;
  }

  // Get console performance data
  const consolePerformance = arrayUtils.groupBy(
    sessions.filter(s => s.console),
    'console._id'
  );

  const topConsoles = Object.entries(consolePerformance)
    .map(([consoleId, consoleSessions]) => ({
      console: consoleSessions[0].console._id,
      sessions: consoleSessions.length,
      revenue: consoleSessions.reduce((sum, s) => sum + (s.finalAmount || 0), 0),
      hours: consoleSessions.reduce((sum, s) => sum + (s.actualDuration || 0), 0) / 60
    }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 10);

  // Get customer analysis data
  const customerAnalysis = arrayUtils.groupBy(
    sessions.filter(s => s.customer),
    'customer._id'
  );

  const topCustomers = Object.entries(customerAnalysis)
    .map(([customerId, customerSessions]) => ({
      customer: customerSessions[0].customer._id,
      sessions: customerSessions.length,
      totalSpent: customerSessions.reduce((sum, s) => sum + (s.finalAmount || 0), 0),
      hours: customerSessions.reduce((sum, s) => sum + (s.actualDuration || 0), 0) / 60
    }))
    .sort((a, b) => b.totalSpent - a.totalSpent)
    .slice(0, 10);

  // Generate time analytics
  const dailyBreakdown = [];
  const currentDate = new Date(start);
  
  while (currentDate <= end) {
    const dayStart = dateUtils.getStartOfDay(currentDate);
    const dayEnd = dateUtils.getEndOfDay(currentDate);
    
    const daySessions = sessions.filter(s => 
      s.startTime >= dayStart && s.startTime <= dayEnd
    );

    dailyBreakdown.push({
      date: new Date(currentDate),
      sessions: daySessions.length,
      revenue: daySessions.reduce((sum, s) => sum + (s.finalAmount || 0), 0),
      hours: daySessions.reduce((sum, s) => sum + (s.actualDuration || 0), 0) / 60
    });

    currentDate.setDate(currentDate.getDate() + 1);
  }

  return {
    revenue,
    sessions: sessionStats,
    consoles: {
      total: await Console.countDocuments({ isActive: true }),
      topPerforming: topConsoles
    },
    customers: {
      total: await Customer.countDocuments({ isActive: true }),
      topSpenders: topCustomers
    },
    timeAnalytics: {
      dailyBreakdown
    }
  };
}

module.exports = router;
