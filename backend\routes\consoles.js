const express = require('express');
const Joi = require('joi');
const { Console, Session } = require('../models');
const {
  authenticateToken,
  requirePermission
} = require('../middleware/auth');
const { validate, validateQuery, schemas, querySchemas } = require('../middleware/validation');
const { catchAsync, AppError, sendSuccess, sendPaginatedResponse } = require('../middleware/errorHandler');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// @desc    Get all consoles
// @route   GET /api/consoles
// @access  Private
router.get('/',
  validateQuery(Joi.object({
    ...querySchemas.pagination,
    status: Joi.string().valid('available', 'active', 'maintenance', 'offline').optional(),
    type: Joi.string().valid('PS5', 'PS4', 'PS4_PRO', 'XBOX_SERIES_X', 'XBOX_SERIES_S', 'XBOX_ONE', 'PC', 'NINTENDO_SWITCH').optional(),
    search: Joi.string().trim().optional()
  })),
  catchAsync(async (req, res, next) => {
    const { page, limit, sort, order, status, type, search } = req.query;

    // Build query
    const query = { isActive: true };
    if (status) query.status = status;
    if (type) query.type = type;
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { consoleId: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort object
    const sortObj = {};
    if (sort) {
      sortObj[sort] = order === 'asc' ? 1 : -1;
    } else {
      sortObj.name = 1; // Default sort by name
    }

    // Execute query with pagination
    const consoles = await Console.find(query)
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name')
      .populate('currentSession')
      .sort(sortObj)
      .limit(limit)
      .skip((page - 1) * limit);

    const total = await Console.countDocuments(query);

    sendPaginatedResponse(res, consoles, page, limit, total, 'Consoles retrieved successfully', 'تم استرداد الأجهزة بنجاح');
  })
);

// @desc    Get console by ID
// @route   GET /api/consoles/:id
// @access  Private
router.get('/:id',
  catchAsync(async (req, res, next) => {
    const console = await Console.findById(req.params.id)
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name')
      .populate('currentSession');

    if (!console) {
      return next(new AppError('Console not found', 404, 'الجهاز غير موجود'));
    }

    sendSuccess(res, 200, { console }, 'Console retrieved successfully', 'تم استرداد الجهاز بنجاح');
  })
);

// @desc    Create new console
// @route   POST /api/consoles
// @access  Private (Requires canManageConsoles permission)
router.post('/',
  requirePermission('canManageConsoles'),
  validate(schemas.createConsole),
  catchAsync(async (req, res, next) => {
    const { name, consoleId, type, hourlyPrice, location, specifications } = req.body;

    // Check if console ID already exists
    const existingConsole = await Console.findOne({ consoleId });
    if (existingConsole) {
      return next(new AppError('Console ID already exists', 400, 'رقم الجهاز موجود بالفعل'));
    }

    // Create console
    const console = await Console.create({
      name,
      consoleId,
      type,
      hourlyPrice,
      location,
      specifications,
      createdBy: req.user._id
    });

    const populatedConsole = await Console.findById(console._id)
      .populate('createdBy', 'name');

    sendSuccess(res, 201, { console: populatedConsole }, 'Console created successfully', 'تم إنشاء الجهاز بنجاح');
  })
);

// @desc    Update console
// @route   PUT /api/consoles/:id
// @access  Private (Requires canManageConsoles permission)
router.put('/:id',
  requirePermission('canManageConsoles'),
  validate(schemas.updateConsole),
  catchAsync(async (req, res, next) => {
    const console = await Console.findById(req.params.id);

    if (!console) {
      return next(new AppError('Console not found', 404, 'الجهاز غير موجود'));
    }

    // Check if console is currently active and trying to change critical settings
    if (console.status === 'active' && (req.body.status === 'maintenance' || req.body.status === 'offline')) {
      const activeSession = await Session.findOne({ console: console._id, status: 'active' });
      if (activeSession) {
        return next(new AppError('Cannot change status while console has active session', 400, 'لا يمكن تغيير الحالة أثناء وجود جلسة نشطة'));
      }
    }

    // Update console
    const updatedConsole = await Console.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedBy: req.user._id },
      { new: true, runValidators: true }
    ).populate('createdBy', 'name').populate('updatedBy', 'name');

    sendSuccess(res, 200, { console: updatedConsole }, 'Console updated successfully', 'تم تحديث الجهاز بنجاح');
  })
);

// @desc    Delete console
// @route   DELETE /api/consoles/:id
// @access  Private (Requires canManageConsoles permission)
router.delete('/:id',
  requirePermission('canManageConsoles'),
  catchAsync(async (req, res, next) => {
    const console = await Console.findById(req.params.id);

    if (!console) {
      return next(new AppError('Console not found', 404, 'الجهاز غير موجود'));
    }

    // Check if console has active sessions
    const activeSession = await Session.findOne({ console: console._id, status: { $in: ['active', 'paused'] } });
    if (activeSession) {
      return next(new AppError('Cannot delete console with active sessions', 400, 'لا يمكن حذف جهاز له جلسات نشطة'));
    }

    // Soft delete by setting isActive to false
    await Console.findByIdAndUpdate(req.params.id, { isActive: false, updatedBy: req.user._id });

    sendSuccess(res, 200, null, 'Console deleted successfully', 'تم حذف الجهاز بنجاح');
  })
);

// @desc    Get available consoles
// @route   GET /api/consoles/available
// @access  Private
router.get('/status/available',
  catchAsync(async (req, res, next) => {
    const consoles = await Console.getAvailableConsoles();

    sendSuccess(res, 200, { consoles }, 'Available consoles retrieved', 'تم استرداد الأجهزة المتاحة');
  })
);

// @desc    Get console statistics
// @route   GET /api/consoles/stats
// @access  Private
router.get('/analytics/stats',
  catchAsync(async (req, res, next) => {
    const stats = await Console.getStatistics();

    sendSuccess(res, 200, { stats }, 'Console statistics retrieved', 'تم استرداد إحصائيات الأجهزة');
  })
);

// @desc    Update console status
// @route   PATCH /api/consoles/:id/status
// @access  Private (Requires canManageConsoles permission)
router.patch('/:id/status',
  requirePermission('canManageConsoles'),
  catchAsync(async (req, res, next) => {
    const { status } = req.body;

    if (!['available', 'maintenance', 'offline'].includes(status)) {
      return next(new AppError('Invalid status', 400, 'حالة غير صحيحة'));
    }

    const console = await Console.findById(req.params.id);

    if (!console) {
      return next(new AppError('Console not found', 404, 'الجهاز غير موجود'));
    }

    // Check if console has active session when trying to set to maintenance/offline
    if (['maintenance', 'offline'].includes(status) && console.status === 'active') {
      const activeSession = await Session.findOne({ console: console._id, status: 'active' });
      if (activeSession) {
        return next(new AppError('Cannot change status while console has active session', 400, 'لا يمكن تغيير الحالة أثناء وجود جلسة نشطة'));
      }
    }

    console.status = status;
    console.updatedBy = req.user._id;
    await console.save();

    sendSuccess(res, 200, { console }, 'Console status updated', 'تم تحديث حالة الجهاز');
  })
);

// @desc    Get console session history
// @route   GET /api/consoles/:id/sessions
// @access  Private
router.get('/:id/sessions',
  validateQuery(querySchemas.pagination),
  catchAsync(async (req, res, next) => {
    const { page, limit } = req.query;
    const consoleId = req.params.id;

    // Check if console exists
    const console = await Console.findById(consoleId);
    if (!console) {
      return next(new AppError('Console not found', 404, 'الجهاز غير موجود'));
    }

    // Get sessions for this console
    const sessions = await Session.find({ console: consoleId })
      .populate('customer', 'name phone membershipType')
      .populate('createdBy', 'name')
      .populate('endedBy', 'name')
      .sort({ startTime: -1 })
      .limit(limit)
      .skip((page - 1) * limit);

    const total = await Session.countDocuments({ console: consoleId });

    sendPaginatedResponse(res, sessions, page, limit, total, 'Console sessions retrieved', 'تم استرداد جلسات الجهاز');
  })
);

// @desc    Update console maintenance info
// @route   PATCH /api/consoles/:id/maintenance
// @access  Private (Requires canManageConsoles permission)
router.patch('/:id/maintenance',
  requirePermission('canManageConsoles'),
  catchAsync(async (req, res, next) => {
    const { lastMaintenanceDate, nextMaintenanceDate, maintenanceNotes, isUnderMaintenance } = req.body;

    const console = await Console.findById(req.params.id);

    if (!console) {
      return next(new AppError('Console not found', 404, 'الجهاز غير موجود'));
    }

    // Update maintenance info
    if (lastMaintenanceDate) console.maintenance.lastMaintenanceDate = lastMaintenanceDate;
    if (nextMaintenanceDate) console.maintenance.nextMaintenanceDate = nextMaintenanceDate;
    if (maintenanceNotes) console.maintenance.maintenanceNotes = maintenanceNotes;
    if (isUnderMaintenance !== undefined) {
      console.maintenance.isUnderMaintenance = isUnderMaintenance;
      // Update status based on maintenance state
      if (isUnderMaintenance) {
        console.status = 'maintenance';
      } else if (console.status === 'maintenance') {
        console.status = 'available';
      }
    }

    console.updatedBy = req.user._id;
    await console.save();

    sendSuccess(res, 200, { console }, 'Console maintenance info updated', 'تم تحديث معلومات صيانة الجهاز');
  })
);

module.exports = router;
