const moment = require('moment');

// Date and time utilities
const dateUtils = {
  // Get start and end of day
  getStartOfDay: (date = new Date()) => {
    const start = new Date(date);
    start.setHours(0, 0, 0, 0);
    return start;
  },

  getEndOfDay: (date = new Date()) => {
    const end = new Date(date);
    end.setHours(23, 59, 59, 999);
    return end;
  },

  // Get start and end of week
  getStartOfWeek: (date = new Date()) => {
    const start = new Date(date);
    const day = start.getDay();
    const diff = start.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    start.setDate(diff);
    start.setHours(0, 0, 0, 0);
    return start;
  },

  getEndOfWeek: (date = new Date()) => {
    const end = new Date(date);
    const day = end.getDay();
    const diff = end.getDate() - day + (day === 0 ? 0 : 7);
    end.setDate(diff);
    end.setHours(23, 59, 59, 999);
    return end;
  },

  // Get start and end of month
  getStartOfMonth: (date = new Date()) => {
    const start = new Date(date.getFullYear(), date.getMonth(), 1);
    start.setHours(0, 0, 0, 0);
    return start;
  },

  getEndOfMonth: (date = new Date()) => {
    const end = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    end.setHours(23, 59, 59, 999);
    return end;
  },

  // Get start and end of year
  getStartOfYear: (date = new Date()) => {
    const start = new Date(date.getFullYear(), 0, 1);
    start.setHours(0, 0, 0, 0);
    return start;
  },

  getEndOfYear: (date = new Date()) => {
    const end = new Date(date.getFullYear(), 11, 31);
    end.setHours(23, 59, 59, 999);
    return end;
  },

  // Format date for Arabic display
  formatDateArabic: (date) => {
    return new Date(date).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    });
  },

  // Format time for Arabic display
  formatTimeArabic: (date) => {
    return new Date(date).toLocaleTimeString('ar-EG', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  },

  // Calculate duration between two dates in minutes
  calculateDuration: (startTime, endTime) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    return Math.floor((end - start) / (1000 * 60));
  },

  // Format duration in Arabic
  formatDurationArabic: (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours === 0) {
      return `${mins} دقيقة`;
    } else if (mins === 0) {
      return `${hours} ساعة`;
    } else {
      return `${hours} ساعة و ${mins} دقيقة`;
    }
  }
};

// Number and currency utilities
const numberUtils = {
  // Format currency in Egyptian Pounds
  formatCurrency: (amount, currency = 'EGP') => {
    const formatted = new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
    
    return formatted;
  },

  // Format number with Arabic numerals
  formatNumberArabic: (number) => {
    return new Intl.NumberFormat('ar-EG').format(number);
  },

  // Round to 2 decimal places
  roundToTwo: (num) => {
    return Math.round((num + Number.EPSILON) * 100) / 100;
  },

  // Calculate percentage
  calculatePercentage: (value, total) => {
    if (total === 0) return 0;
    return Math.round((value / total) * 100);
  },

  // Calculate tax amount
  calculateTax: (amount, taxRate = 0.14) => {
    return numberUtils.roundToTwo(amount * taxRate);
  },

  // Calculate discount amount
  calculateDiscount: (amount, discountPercent) => {
    return numberUtils.roundToTwo(amount * (discountPercent / 100));
  }
};

// String utilities
const stringUtils = {
  // Generate random ID
  generateId: (prefix = '', length = 8) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = prefix;
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  // Capitalize first letter
  capitalize: (str) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  },

  // Truncate string
  truncate: (str, length = 50) => {
    if (str.length <= length) return str;
    return str.substring(0, length) + '...';
  },

  // Clean and normalize Arabic text
  normalizeArabic: (text) => {
    return text
      .replace(/ي/g, 'ى')
      .replace(/ة/g, 'ه')
      .trim();
  },

  // Generate session ID
  generateSessionId: () => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9).toUpperCase();
    return `SES-${timestamp}-${random}`;
  },

  // Generate report ID
  generateReportId: () => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 6).toUpperCase();
    return `RPT-${timestamp}-${random}`;
  }
};

// Validation utilities
const validationUtils = {
  // Check if email is valid
  isValidEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Check if phone number is valid (Egyptian format)
  isValidPhone: (phone) => {
    const phoneRegex = /^(\+20|0)?1[0125]\d{8}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  },

  // Check if string contains only Arabic characters
  isArabic: (text) => {
    const arabicRegex = /^[\u0600-\u06FF\s]+$/;
    return arabicRegex.test(text);
  },

  // Check if string contains only English characters
  isEnglish: (text) => {
    const englishRegex = /^[a-zA-Z\s]+$/;
    return englishRegex.test(text);
  },

  // Sanitize input
  sanitizeInput: (input) => {
    if (typeof input !== 'string') return input;
    return input.trim().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  }
};

// Array utilities
const arrayUtils = {
  // Remove duplicates from array
  removeDuplicates: (arr) => {
    return [...new Set(arr)];
  },

  // Group array by key
  groupBy: (arr, key) => {
    return arr.reduce((groups, item) => {
      const group = item[key];
      groups[group] = groups[group] || [];
      groups[group].push(item);
      return groups;
    }, {});
  },

  // Sort array by multiple keys
  sortBy: (arr, keys) => {
    return arr.sort((a, b) => {
      for (let key of keys) {
        const aVal = a[key];
        const bVal = b[key];
        if (aVal < bVal) return -1;
        if (aVal > bVal) return 1;
      }
      return 0;
    });
  },

  // Paginate array
  paginate: (arr, page = 1, limit = 10) => {
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    return {
      data: arr.slice(startIndex, endIndex),
      total: arr.length,
      page,
      limit,
      totalPages: Math.ceil(arr.length / limit)
    };
  }
};

// Object utilities
const objectUtils = {
  // Deep clone object
  deepClone: (obj) => {
    return JSON.parse(JSON.stringify(obj));
  },

  // Pick specific keys from object
  pick: (obj, keys) => {
    const result = {};
    keys.forEach(key => {
      if (obj.hasOwnProperty(key)) {
        result[key] = obj[key];
      }
    });
    return result;
  },

  // Omit specific keys from object
  omit: (obj, keys) => {
    const result = { ...obj };
    keys.forEach(key => {
      delete result[key];
    });
    return result;
  },

  // Check if object is empty
  isEmpty: (obj) => {
    return Object.keys(obj).length === 0;
  }
};

// Export all utilities
module.exports = {
  dateUtils,
  numberUtils,
  stringUtils,
  validationUtils,
  arrayUtils,
  objectUtils
};
