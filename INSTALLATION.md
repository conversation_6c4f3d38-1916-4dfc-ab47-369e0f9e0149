# دليل التثبيت والتشغيل
# Installation and Setup Guide

## متطلبات النظام / System Requirements

### الحد الأدنى / Minimum Requirements
- **Node.js**: 16.0.0 أو أحدث / or newer
- **MongoDB**: 5.0 أو أحدث / or newer  
- **npm**: 8.0.0 أو أحدث / or newer
- **RAM**: 4GB
- **Storage**: 2GB مساحة فارغة / free space

### الموصى به / Recommended
- **Node.js**: 18.0.0 أو أحدث / or newer
- **MongoDB**: 6.0 أو أحدث / or newer
- **RAM**: 8GB أو أكثر / or more
- **Storage**: 5GB مساحة فارغة / free space

## خطوات التثبيت / Installation Steps

### 1. تحضير البيئة / Environment Setup

#### تثبيت Node.js / Install Node.js
```bash
# تحقق من الإصدار المثبت / Check installed version
node --version
npm --version

# إذا لم يكن مثبتاً، قم بتحميله من / If not installed, download from:
# https://nodejs.org/
```

#### تثبيت MongoDB / Install MongoDB
```bash
# Windows - استخدم MongoDB Compass أو / Use MongoDB Compass or:
# https://www.mongodb.com/try/download/community

# macOS
brew tap mongodb/brew
brew install mongodb-community

# Ubuntu/Debian
sudo apt-get install mongodb

# تشغيل MongoDB / Start MongoDB
mongod
```

### 2. استنساخ المشروع / Clone Project
```bash
git clone https://github.com/your-username/playstation-shop-management.git
cd playstation-shop-management
```

### 3. إعداد Backend

#### تثبيت التبعيات / Install Dependencies
```bash
cd backend
npm install
```

#### إعداد متغيرات البيئة / Environment Variables
```bash
# إنشاء ملف .env / Create .env file
cp .env.example .env

# تحرير الملف وإضافة القيم المناسبة / Edit file with appropriate values
```

**محتوى ملف .env / .env file content:**
```env
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/playstation-shop
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=admin123456
FRONTEND_URL=http://localhost:3000
```

#### تشغيل Backend / Start Backend
```bash
# للتطوير / Development
npm run dev

# للإنتاج / Production
npm start
```

### 4. إعداد Frontend

#### تثبيت التبعيات / Install Dependencies
```bash
cd ../frontend
npm install
```

#### إعداد متغيرات البيئة / Environment Variables
```bash
# إنشاء ملف .env / Create .env file
cp .env.example .env
```

**محتوى ملف .env / .env file content:**
```env
VITE_API_URL=http://localhost:5000/api
VITE_APP_NAME=PlayStation Shop Management
VITE_APP_VERSION=1.0.0
```

#### تشغيل Frontend / Start Frontend
```bash
# للتطوير / Development
npm run dev

# بناء للإنتاج / Build for production
npm run build
```

## التحقق من التثبيت / Verify Installation

### 1. تحقق من Backend
```bash
# يجب أن ترى / You should see:
# 🚀 Server running on port 5000
# 🚀 الخادم يعمل على المنفذ 5000
# ✅ Connected to MongoDB
# ✅ تم الاتصال بقاعدة البيانات
```

### 2. تحقق من Frontend
```bash
# افتح المتصفح وانتقل إلى / Open browser and go to:
# http://localhost:3000
```

### 3. تسجيل الدخول / Login Test
استخدم البيانات الافتراضية / Use default credentials:
- **البريد الإلكتروني / Email**: <EMAIL>
- **كلمة المرور / Password**: admin123456

## استكشاف الأخطاء / Troubleshooting

### مشاكل شائعة / Common Issues

#### 1. خطأ في الاتصال بقاعدة البيانات / Database Connection Error
```bash
# تأكد من تشغيل MongoDB / Ensure MongoDB is running
mongod

# تحقق من الاتصال / Test connection
mongo
```

#### 2. خطأ في المنفذ / Port Error
```bash
# تغيير المنفذ في ملف .env / Change port in .env file
PORT=5001
```

#### 3. مشاكل في التبعيات / Dependencies Issues
```bash
# حذف node_modules وإعادة التثبيت / Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### 4. مشاكل في CORS
```bash
# تأكد من إعداد FRONTEND_URL في backend/.env / Ensure FRONTEND_URL is set in backend/.env
FRONTEND_URL=http://localhost:3000
```

### سجلات الأخطاء / Error Logs

#### Backend Logs
```bash
# عرض السجلات / View logs
tail -f logs/app.log

# أو في وضع التطوير / Or in development mode
npm run dev
```

#### Frontend Logs
```bash
# افتح أدوات المطور في المتصفح / Open browser developer tools
# F12 -> Console
```

## الإنتاج / Production Deployment

### 1. إعداد البيئة / Environment Setup
```bash
# تغيير NODE_ENV / Change NODE_ENV
NODE_ENV=production

# استخدام قاعدة بيانات منفصلة / Use separate database
MONGODB_URI=mongodb://localhost:27017/playstation-shop-prod
```

### 2. بناء Frontend / Build Frontend
```bash
cd frontend
npm run build
```

### 3. تشغيل Backend / Start Backend
```bash
cd backend
npm start
```

### 4. خادم الويب / Web Server
```bash
# استخدم nginx أو apache لتقديم الملفات الثابتة / Use nginx or apache for static files
# مثال nginx configuration:

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## النسخ الاحتياطي / Backup

### قاعدة البيانات / Database Backup
```bash
# إنشاء نسخة احتياطية / Create backup
mongodump --db playstation-shop --out ./backup/$(date +%Y%m%d)

# استعادة النسخة الاحتياطية / Restore backup
mongorestore --db playstation-shop ./backup/20231201/playstation-shop
```

### الملفات / Files Backup
```bash
# نسخ احتياطية للملفات المرفوعة / Backup uploaded files
tar -czf uploads-backup-$(date +%Y%m%d).tar.gz backend/uploads/
```

## التحديث / Updates

### تحديث التبعيات / Update Dependencies
```bash
# Backend
cd backend
npm update

# Frontend  
cd frontend
npm update
```

### تحديث قاعدة البيانات / Database Updates
```bash
# تشغيل migrations إذا وجدت / Run migrations if available
npm run migrate
```

## الدعم / Support

إذا واجهت أي مشاكل / If you encounter any issues:

1. تحقق من السجلات / Check logs
2. راجع قسم استكشاف الأخطاء / Review troubleshooting section  
3. ابحث في Issues على GitHub / Search GitHub Issues
4. أنشئ Issue جديد / Create new Issue

## الأمان / Security

### للإنتاج / For Production:
- غيّر JWT_SECRET / Change JWT_SECRET
- استخدم HTTPS / Use HTTPS
- قم بتحديث كلمات المرور الافتراضية / Update default passwords
- فعّل firewall / Enable firewall
- قم بتحديث النظام بانتظام / Regular system updates
