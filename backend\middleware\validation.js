const Joi = require('joi');

// Validation schemas
const schemas = {
  // User validation
  registerUser: Joi.object({
    name: Joi.string().trim().min(2).max(50).required().messages({
      'string.empty': 'الاسم مطلوب',
      'string.min': 'الاسم يجب أن يكون على الأقل حرفين',
      'string.max': 'الاسم يجب أن يكون أقل من 50 حرف',
      'any.required': 'الاسم مطلوب'
    }),
    email: Joi.string().email().lowercase().trim().required().messages({
      'string.email': 'البريد الإلكتروني غير صحيح',
      'string.empty': 'البريد الإلكتروني مطلوب',
      'any.required': 'البريد الإلكتروني مطلوب'
    }),
    password: Joi.string().min(6).max(128).required().messages({
      'string.min': 'كلمة المرور يجب أن تكون على الأقل 6 أحرف',
      'string.max': 'كلمة المرور يجب أن تكون أقل من 128 حرف',
      'string.empty': 'كلمة المرور مطلوبة',
      'any.required': 'كلمة المرور مطلوبة'
    }),
    role: Joi.string().valid('admin', 'staff').default('staff').messages({
      'any.only': 'الدور يجب أن يكون admin أو staff'
    }),
    permissions: Joi.object({
      canManageConsoles: Joi.boolean().default(false),
      canManageSessions: Joi.boolean().default(true),
      canManageCustomers: Joi.boolean().default(true),
      canViewReports: Joi.boolean().default(false),
      canManageUsers: Joi.boolean().default(false),
      canManageSettings: Joi.boolean().default(false)
    }).optional()
  }),

  loginUser: Joi.object({
    email: Joi.string().email().lowercase().trim().required().messages({
      'string.email': 'البريد الإلكتروني غير صحيح',
      'string.empty': 'البريد الإلكتروني مطلوب',
      'any.required': 'البريد الإلكتروني مطلوب'
    }),
    password: Joi.string().required().messages({
      'string.empty': 'كلمة المرور مطلوبة',
      'any.required': 'كلمة المرور مطلوبة'
    })
  }),

  // Console validation
  createConsole: Joi.object({
    name: Joi.string().trim().min(1).max(50).required().messages({
      'string.empty': 'اسم الجهاز مطلوب',
      'string.max': 'اسم الجهاز يجب أن يكون أقل من 50 حرف',
      'any.required': 'اسم الجهاز مطلوب'
    }),
    consoleId: Joi.string().trim().uppercase().pattern(/^[A-Z0-9-]+$/).required().messages({
      'string.pattern.base': 'رقم الجهاز يجب أن يحتوي على أحرف إنجليزية وأرقام فقط',
      'string.empty': 'رقم الجهاز مطلوب',
      'any.required': 'رقم الجهاز مطلوب'
    }),
    type: Joi.string().valid('PS5', 'PS4', 'PS4_PRO', 'XBOX_SERIES_X', 'XBOX_SERIES_S', 'XBOX_ONE', 'PC', 'NINTENDO_SWITCH').required().messages({
      'any.only': 'نوع الجهاز غير صحيح',
      'any.required': 'نوع الجهاز مطلوب'
    }),
    hourlyPrice: Joi.number().min(0).max(1000).required().messages({
      'number.min': 'سعر الساعة يجب أن يكون أكبر من أو يساوي صفر',
      'number.max': 'سعر الساعة يجب أن يكون أقل من 1000',
      'any.required': 'سعر الساعة مطلوب'
    }),
    location: Joi.string().trim().max(100).optional(),
    specifications: Joi.object({
      storage: Joi.string().trim().optional(),
      controllers: Joi.number().min(1).max(8).default(2),
      accessories: Joi.array().items(Joi.string().trim()).optional(),
      notes: Joi.string().trim().max(500).optional()
    }).optional()
  }),

  updateConsole: Joi.object({
    name: Joi.string().trim().min(1).max(50).optional(),
    hourlyPrice: Joi.number().min(0).max(1000).optional(),
    status: Joi.string().valid('available', 'active', 'maintenance', 'offline').optional(),
    location: Joi.string().trim().max(100).optional(),
    specifications: Joi.object({
      storage: Joi.string().trim().optional(),
      controllers: Joi.number().min(1).max(8).optional(),
      accessories: Joi.array().items(Joi.string().trim()).optional(),
      notes: Joi.string().trim().max(500).optional()
    }).optional()
  }),

  // Customer validation
  createCustomer: Joi.object({
    name: Joi.string().trim().min(1).max(100).required().messages({
      'string.empty': 'اسم العميل مطلوب',
      'string.max': 'اسم العميل يجب أن يكون أقل من 100 حرف',
      'any.required': 'اسم العميل مطلوب'
    }),
    phone: Joi.string().trim().pattern(/^[\+]?[0-9\-\(\)\s]+$/).max(20).optional().messages({
      'string.pattern.base': 'رقم الهاتف غير صحيح'
    }),
    email: Joi.string().email().lowercase().trim().optional().messages({
      'string.email': 'البريد الإلكتروني غير صحيح'
    }),
    dateOfBirth: Joi.date().max('now').optional().messages({
      'date.max': 'تاريخ الميلاد يجب أن يكون في الماضي'
    }),
    membershipType: Joi.string().valid('regular', 'vip', 'premium').default('regular'),
    prepaidBalance: Joi.number().min(0).default(0).optional(),
    notes: Joi.string().trim().max(1000).optional()
  }),

  updateCustomer: Joi.object({
    name: Joi.string().trim().min(1).max(100).optional(),
    phone: Joi.string().trim().pattern(/^[\+]?[0-9\-\(\)\s]+$/).max(20).optional(),
    email: Joi.string().email().lowercase().trim().optional(),
    dateOfBirth: Joi.date().max('now').optional(),
    membershipType: Joi.string().valid('regular', 'vip', 'premium').optional(),
    notes: Joi.string().trim().max(1000).optional()
  }),

  // Session validation
  startSession: Joi.object({
    consoleId: Joi.string().required().messages({
      'any.required': 'معرف الجهاز مطلوب'
    }),
    customerId: Joi.string().optional(),
    customerName: Joi.string().trim().max(100).optional(),
    plannedDuration: Joi.number().min(1).max(1440).optional().messages({
      'number.min': 'المدة المخططة يجب أن تكون على الأقل دقيقة واحدة',
      'number.max': 'المدة المخططة لا يمكن أن تتجاوز 24 ساعة'
    }),
    notes: Joi.string().trim().max(1000).optional()
  }),

  endSession: Joi.object({
    paymentMethod: Joi.string().valid('cash', 'prepaid', 'card', 'mixed').default('cash'),
    discountAmount: Joi.number().min(0).default(0).optional(),
    discountReason: Joi.string().trim().max(200).optional(),
    notes: Joi.string().trim().max(1000).optional()
  }),

  // Report validation
  generateReport: Joi.object({
    title: Joi.string().trim().min(1).max(200).required().messages({
      'string.empty': 'عنوان التقرير مطلوب',
      'any.required': 'عنوان التقرير مطلوب'
    }),
    type: Joi.string().valid('daily', 'weekly', 'monthly', 'yearly', 'custom', 'console_performance', 'customer_analysis', 'revenue_analysis').required(),
    startDate: Joi.date().required().messages({
      'any.required': 'تاريخ البداية مطلوب'
    }),
    endDate: Joi.date().min(Joi.ref('startDate')).required().messages({
      'date.min': 'تاريخ النهاية يجب أن يكون بعد أو يساوي تاريخ البداية',
      'any.required': 'تاريخ النهاية مطلوب'
    }),
    format: Joi.string().valid('json', 'pdf', 'excel', 'csv').default('json'),
    filters: Joi.object({
      consoles: Joi.array().items(Joi.string()).optional(),
      customers: Joi.array().items(Joi.string()).optional(),
      paymentMethods: Joi.array().items(Joi.string().valid('cash', 'prepaid', 'card', 'mixed')).optional(),
      sessionStatus: Joi.array().items(Joi.string().valid('active', 'completed', 'cancelled', 'paused')).optional()
    }).optional()
  })
};

// Validation middleware factory
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
      convert: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        message_ar: 'فشل في التحقق من البيانات',
        errors
      });
    }

    // Replace req.body with validated and sanitized data
    req.body = value;
    next();
  };
};

// Query parameter validation
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true,
      convert: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      return res.status(400).json({
        success: false,
        error: 'Query validation failed',
        message_ar: 'فشل في التحقق من معاملات الاستعلام',
        errors
      });
    }

    req.query = value;
    next();
  };
};

// Common query schemas
const querySchemas = {
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    sort: Joi.string().optional(),
    order: Joi.string().valid('asc', 'desc').default('desc')
  }),

  dateRange: Joi.object({
    startDate: Joi.date().optional(),
    endDate: Joi.date().min(Joi.ref('startDate')).optional(),
    period: Joi.string().valid('today', 'yesterday', 'week', 'month', 'year').optional()
  }),

  search: Joi.object({
    q: Joi.string().trim().min(1).optional(),
    fields: Joi.array().items(Joi.string()).optional()
  })
};

module.exports = {
  validate,
  validateQuery,
  schemas,
  querySchemas
};
