import { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuthStore } from './store/authStore'
import { useSettingsStore } from './store/settingsStore'

// Layout Components
import Layout from './components/Layout/Layout'
import AuthLayout from './components/Layout/AuthLayout'

// Page Components
import LoginPage from './pages/Auth/LoginPage'
import DashboardPage from './pages/Dashboard/DashboardPage'
import ConsolesPage from './pages/Consoles/ConsolesPage'
import SessionsPage from './pages/Sessions/SessionsPage'
import CustomersPage from './pages/Customers/CustomersPage'
import ReportsPage from './pages/Reports/ReportsPage'
import SettingsPage from './pages/Settings/SettingsPage'
import UsersPage from './pages/Users/<USER>'

// Components
import LoadingSpinner from './components/UI/LoadingSpinner'
import ProtectedRoute from './components/Auth/ProtectedRoute'

// Hooks
import { useAudioNotifications } from './hooks/useAudioNotifications'

function App() {
  const { user, isLoading, checkAuth } = useAuthStore()
  const { loadSettings } = useSettingsStore()
  const { initializeAudio } = useAudioNotifications()

  useEffect(() => {
    // Initialize app
    const initializeApp = async () => {
      try {
        // Load public settings first
        await loadSettings()
        
        // Check authentication
        await checkAuth()
        
        // Initialize audio notifications
        initializeAudio()
      } catch (error) {
        console.error('App initialization error:', error)
      }
    }

    initializeApp()
  }, [checkAuth, loadSettings, initializeAudio])

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <LoadingSpinner size="large" />
          <p className="mt-4 text-gray-600 font-medium">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <AnimatePresence mode="wait">
        <Routes>
          {/* Public Routes */}
          <Route
            path="/login"
            element={
              user ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <AuthLayout>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <LoginPage />
                  </motion.div>
                </AuthLayout>
              )
            }
          />

          {/* Protected Routes */}
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <Layout>
                  <AnimatePresence mode="wait">
                    <Routes>
                      {/* Dashboard */}
                      <Route
                        path="/dashboard"
                        element={
                          <motion.div
                            key="dashboard"
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -20 }}
                            transition={{ duration: 0.3 }}
                          >
                            <DashboardPage />
                          </motion.div>
                        }
                      />

                      {/* Consoles Management */}
                      <Route
                        path="/consoles"
                        element={
                          <ProtectedRoute requiredPermission="canManageConsoles">
                            <motion.div
                              key="consoles"
                              initial={{ opacity: 0, x: 20 }}
                              animate={{ opacity: 1, x: 0 }}
                              exit={{ opacity: 0, x: -20 }}
                              transition={{ duration: 0.3 }}
                            >
                              <ConsolesPage />
                            </motion.div>
                          </ProtectedRoute>
                        }
                      />

                      {/* Sessions Management */}
                      <Route
                        path="/sessions"
                        element={
                          <ProtectedRoute requiredPermission="canManageSessions">
                            <motion.div
                              key="sessions"
                              initial={{ opacity: 0, x: 20 }}
                              animate={{ opacity: 1, x: 0 }}
                              exit={{ opacity: 0, x: -20 }}
                              transition={{ duration: 0.3 }}
                            >
                              <SessionsPage />
                            </motion.div>
                          </ProtectedRoute>
                        }
                      />

                      {/* Customers Management */}
                      <Route
                        path="/customers"
                        element={
                          <ProtectedRoute requiredPermission="canManageCustomers">
                            <motion.div
                              key="customers"
                              initial={{ opacity: 0, x: 20 }}
                              animate={{ opacity: 1, x: 0 }}
                              exit={{ opacity: 0, x: -20 }}
                              transition={{ duration: 0.3 }}
                            >
                              <CustomersPage />
                            </motion.div>
                          </ProtectedRoute>
                        }
                      />

                      {/* Reports */}
                      <Route
                        path="/reports"
                        element={
                          <ProtectedRoute requiredPermission="canViewReports">
                            <motion.div
                              key="reports"
                              initial={{ opacity: 0, x: 20 }}
                              animate={{ opacity: 1, x: 0 }}
                              exit={{ opacity: 0, x: -20 }}
                              transition={{ duration: 0.3 }}
                            >
                              <ReportsPage />
                            </motion.div>
                          </ProtectedRoute>
                        }
                      />

                      {/* Users Management */}
                      <Route
                        path="/users"
                        element={
                          <ProtectedRoute requiredPermission="canManageUsers">
                            <motion.div
                              key="users"
                              initial={{ opacity: 0, x: 20 }}
                              animate={{ opacity: 1, x: 0 }}
                              exit={{ opacity: 0, x: -20 }}
                              transition={{ duration: 0.3 }}
                            >
                              <UsersPage />
                            </motion.div>
                          </ProtectedRoute>
                        }
                      />

                      {/* Settings */}
                      <Route
                        path="/settings"
                        element={
                          <ProtectedRoute requiredPermission="canManageSettings">
                            <motion.div
                              key="settings"
                              initial={{ opacity: 0, x: 20 }}
                              animate={{ opacity: 1, x: 0 }}
                              exit={{ opacity: 0, x: -20 }}
                              transition={{ duration: 0.3 }}
                            >
                              <SettingsPage />
                            </motion.div>
                          </ProtectedRoute>
                        }
                      />

                      {/* Default redirect */}
                      <Route path="/" element={<Navigate to="/dashboard" replace />} />
                      
                      {/* 404 Page */}
                      <Route
                        path="*"
                        element={
                          <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            className="flex flex-col items-center justify-center min-h-96 text-center"
                          >
                            <div className="text-6xl mb-4">🎮</div>
                            <h1 className="text-2xl font-bold text-gray-800 mb-2">
                              الصفحة غير موجودة
                            </h1>
                            <p className="text-gray-600 mb-6">
                              عذراً، الصفحة التي تبحث عنها غير موجودة
                            </p>
                            <button
                              onClick={() => window.history.back()}
                              className="btn-primary"
                            >
                              العودة للخلف
                            </button>
                          </motion.div>
                        }
                      />
                    </Routes>
                  </AnimatePresence>
                </Layout>
              </ProtectedRoute>
            }
          />
        </Routes>
      </AnimatePresence>
    </div>
  )
}

export default App
