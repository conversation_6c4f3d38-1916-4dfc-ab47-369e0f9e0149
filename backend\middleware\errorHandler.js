const mongoose = require('mongoose');

// Custom error class
class AppError extends Error {
  constructor(message, statusCode, messageAr = null) {
    super(message);
    this.statusCode = statusCode;
    this.messageAr = messageAr;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Handle MongoDB cast errors
const handleCastErrorDB = (err) => {
  const message = `Invalid ${err.path}: ${err.value}`;
  const messageAr = `قيمة غير صحيحة للحقل ${err.path}: ${err.value}`;
  return new AppError(message, 400, messageAr);
};

// Handle MongoDB duplicate key errors
const handleDuplicateFieldsDB = (err) => {
  const field = Object.keys(err.keyValue)[0];
  const value = err.keyValue[field];
  const message = `Duplicate field value: ${field} = ${value}. Please use another value!`;
  const messageAr = `قيمة مكررة للحقل ${field}: ${value}. يرجى استخدام قيمة أخرى!`;
  return new AppError(message, 400, messageAr);
};

// Handle MongoDB validation errors
const handleValidationErrorDB = (err) => {
  const errors = Object.values(err.errors).map(el => el.message);
  const message = `Invalid input data. ${errors.join('. ')}`;
  const messageAr = `بيانات إدخال غير صحيحة. ${errors.join('. ')}`;
  return new AppError(message, 400, messageAr);
};

// Handle JWT errors
const handleJWTError = () => {
  const message = 'Invalid token. Please log in again!';
  const messageAr = 'رمز غير صحيح. يرجى تسجيل الدخول مرة أخرى!';
  return new AppError(message, 401, messageAr);
};

const handleJWTExpiredError = () => {
  const message = 'Your token has expired! Please log in again.';
  const messageAr = 'انتهت صلاحية الرمز! يرجى تسجيل الدخول مرة أخرى.';
  return new AppError(message, 401, messageAr);
};

// Send error response in development
const sendErrorDev = (err, res) => {
  res.status(err.statusCode).json({
    success: false,
    status: err.status,
    error: err.message,
    message_ar: err.messageAr || err.message,
    stack: err.stack,
    details: err
  });
};

// Send error response in production
const sendErrorProd = (err, res) => {
  // Operational, trusted error: send message to client
  if (err.isOperational) {
    res.status(err.statusCode).json({
      success: false,
      status: err.status,
      error: err.message,
      message_ar: err.messageAr || err.message
    });
  } else {
    // Programming or other unknown error: don't leak error details
    console.error('ERROR 💥', err);
    
    res.status(500).json({
      success: false,
      status: 'error',
      error: 'Something went wrong!',
      message_ar: 'حدث خطأ ما!'
    });
  }
};

// Main error handling middleware
const globalErrorHandler = (err, req, res, next) => {
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';

  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(err, res);
  } else {
    let error = { ...err };
    error.message = err.message;

    // Handle specific MongoDB errors
    if (error.name === 'CastError') error = handleCastErrorDB(error);
    if (error.code === 11000) error = handleDuplicateFieldsDB(error);
    if (error.name === 'ValidationError') error = handleValidationErrorDB(error);
    if (error.name === 'JsonWebTokenError') error = handleJWTError();
    if (error.name === 'TokenExpiredError') error = handleJWTExpiredError();

    sendErrorProd(error, res);
  }
};

// Async error handler wrapper
const catchAsync = (fn) => {
  return (req, res, next) => {
    fn(req, res, next).catch(next);
  };
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  console.log('UNHANDLED REJECTION! 💥 Shutting down...');
  console.log(err.name, err.message);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.log('UNCAUGHT EXCEPTION! 💥 Shutting down...');
  console.log(err.name, err.message);
  process.exit(1);
});

// 404 handler for undefined routes
const notFound = (req, res, next) => {
  const message = `Can't find ${req.originalUrl} on this server!`;
  const messageAr = `لا يمكن العثور على ${req.originalUrl} على هذا الخادم!`;
  const err = new AppError(message, 404, messageAr);
  next(err);
};

// Success response helper
const sendSuccess = (res, statusCode = 200, data = null, message = 'Success', messageAr = 'نجح') => {
  const response = {
    success: true,
    status: 'success',
    message,
    message_ar: messageAr
  };

  if (data !== null) {
    response.data = data;
  }

  res.status(statusCode).json(response);
};

// Paginated response helper
const sendPaginatedResponse = (res, data, page, limit, total, message = 'Data retrieved successfully', messageAr = 'تم استرداد البيانات بنجاح') => {
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    success: true,
    status: 'success',
    message,
    message_ar: messageAr,
    data,
    pagination: {
      currentPage: page,
      totalPages,
      totalItems: total,
      itemsPerPage: limit,
      hasNextPage,
      hasPrevPage,
      nextPage: hasNextPage ? page + 1 : null,
      prevPage: hasPrevPage ? page - 1 : null
    }
  });
};

// Log error for monitoring
const logError = (err, req) => {
  const errorLog = {
    timestamp: new Date().toISOString(),
    error: {
      message: err.message,
      stack: err.stack,
      statusCode: err.statusCode
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user ? req.user._id : null
    }
  };

  console.error('Error Log:', JSON.stringify(errorLog, null, 2));
  
  // Here you could send to external logging service
  // like Winston, Sentry, or CloudWatch
};

module.exports = {
  AppError,
  globalErrorHandler,
  catchAsync,
  notFound,
  sendSuccess,
  sendPaginatedResponse,
  logError
};
