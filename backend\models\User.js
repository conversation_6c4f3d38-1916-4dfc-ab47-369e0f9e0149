const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'الاسم مطلوب'],
    trim: true,
    maxlength: [50, 'الاسم يجب أن يكون أقل من 50 حرف']
  },
  email: {
    type: String,
    required: [true, 'البريد الإلكتروني مطلوب'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'البريد الإلكتروني غير صحيح']
  },
  password: {
    type: String,
    required: [true, 'كلمة المرور مطلوبة'],
    minlength: [6, 'كلمة المرور يجب أن تكون على الأقل 6 أحرف']
  },
  role: {
    type: String,
    enum: {
      values: ['admin', 'staff'],
      message: 'الدور يجب أن يكون admin أو staff'
    },
    default: 'staff'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  permissions: {
    canManageConsoles: { type: Boolean, default: false },
    canManageSessions: { type: Boolean, default: true },
    canManageCustomers: { type: Boolean, default: true },
    canViewReports: { type: Boolean, default: false },
    canManageUsers: { type: Boolean, default: false },
    canManageSettings: { type: Boolean, default: false }
  },
  lastLogin: {
    type: Date
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
userSchema.index({ email: 1 });
userSchema.index({ role: 1 });
userSchema.index({ isActive: 1 });

// Virtual for user's full permissions based on role
userSchema.virtual('fullPermissions').get(function() {
  if (this.role === 'admin') {
    return {
      canManageConsoles: true,
      canManageSessions: true,
      canManageCustomers: true,
      canViewReports: true,
      canManageUsers: true,
      canManageSettings: true
    };
  }
  return this.permissions;
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Remove password from JSON output
userSchema.methods.toJSON = function() {
  const userObject = this.toObject();
  delete userObject.password;
  return userObject;
};

// Static method to create default admin
userSchema.statics.createDefaultAdmin = async function() {
  const adminExists = await this.findOne({ role: 'admin' });
  if (!adminExists) {
    const defaultAdmin = new this({
      name: 'مدير النظام',
      email: process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>',
      password: process.env.DEFAULT_ADMIN_PASSWORD || 'admin123456',
      role: 'admin',
      isActive: true
    });
    await defaultAdmin.save();
    console.log('✅ Default admin user created');
    console.log('✅ تم إنشاء مستخدم المدير الافتراضي');
  }
};

module.exports = mongoose.model('User', userSchema);
