const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
    message_ar: 'عدد كبير من الطلبات من هذا العنوان، يرجى المحاولة لاحقاً.'
  }
});
app.use(limiter);

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// Database connection
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/playstation-shop', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('✅ Connected to MongoDB');
  console.log('✅ متصل بقاعدة البيانات');
})
.catch((error) => {
  console.error('❌ MongoDB connection error:', error);
  console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
  process.exit(1);
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'PlayStation Shop API is running',
    message_ar: 'واجهة برمجة التطبيقات لمحل البلايستيشن تعمل بشكل طبيعي',
    timestamp: new Date().toISOString()
  });
});

// Initialize default data
const { initializeDefaults } = require('./models');

// API Routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/consoles', require('./routes/consoles'));
app.use('/api/sessions', require('./routes/sessions'));
app.use('/api/customers', require('./routes/customers'));
app.use('/api/reports', require('./routes/reports'));
app.use('/api/settings', require('./routes/settings'));

// Import error handling middleware
const { globalErrorHandler, notFound } = require('./middleware/errorHandler');

// 404 handler for undefined routes
app.use(notFound);

// Global error handling middleware
app.use(globalErrorHandler);

const PORT = process.env.PORT || 5000;

app.listen(PORT, async () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
  console.log(`📱 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:3000'}`);

  // Initialize default data after server starts
  await initializeDefaults();
});

module.exports = app;
