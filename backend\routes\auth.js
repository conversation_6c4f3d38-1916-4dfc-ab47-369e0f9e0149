const express = require('express');
const { User } = require('../models');
const { 
  generateToken, 
  authenticateToken, 
  requireAdmin, 
  rateLimitLogin, 
  clearLoginAttempts 
} = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');
const { catchAsync, AppError, sendSuccess } = require('../middleware/errorHandler');

const router = express.Router();

// @desc    Register a new user
// @route   POST /api/auth/register
// @access  Private (Admin only)
router.post('/register', 
  authenticateToken,
  requireAdmin,
  validate(schemas.registerUser),
  catchAsync(async (req, res, next) => {
    const { name, email, password, role, permissions } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return next(new AppError('User already exists', 400, 'المستخدم موجود بالفعل'));
    }

    // Create new user
    const userData = {
      name,
      email,
      password,
      role,
      createdBy: req.user._id
    };

    // Set permissions for staff users
    if (role === 'staff' && permissions) {
      userData.permissions = permissions;
    }

    const user = await User.create(userData);

    sendSuccess(res, 201, {
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        permissions: user.fullPermissions,
        isActive: user.isActive,
        createdAt: user.createdAt
      }
    }, 'User created successfully', 'تم إنشاء المستخدم بنجاح');
  })
);

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
router.post('/login',
  rateLimitLogin,
  validate(schemas.loginUser),
  catchAsync(async (req, res, next) => {
    const { email, password } = req.body;
    const ip = req.ip || req.connection.remoteAddress;

    // Find user and include password for comparison
    const user = await User.findOne({ email }).select('+password');

    if (!user || !(await user.comparePassword(password))) {
      return next(new AppError('Invalid email or password', 401, 'البريد الإلكتروني أو كلمة المرور غير صحيحة'));
    }

    if (!user.isActive) {
      return next(new AppError('Account is deactivated', 401, 'الحساب معطل'));
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save({ validateBeforeSave: false });

    // Clear login attempts on successful login
    clearLoginAttempts(ip);

    // Generate token
    const token = generateToken(user._id);

    sendSuccess(res, 200, {
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        permissions: user.fullPermissions,
        isActive: user.isActive,
        lastLogin: user.lastLogin
      }
    }, 'Login successful', 'تم تسجيل الدخول بنجاح');
  })
);

// @desc    Get current user profile
// @route   GET /api/auth/me
// @access  Private
router.get('/me',
  authenticateToken,
  catchAsync(async (req, res, next) => {
    const user = await User.findById(req.user._id);

    sendSuccess(res, 200, {
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        permissions: user.fullPermissions,
        isActive: user.isActive,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt
      }
    }, 'User profile retrieved', 'تم استرداد ملف المستخدم');
  })
);

// @desc    Update current user profile
// @route   PUT /api/auth/me
// @access  Private
router.put('/me',
  authenticateToken,
  catchAsync(async (req, res, next) => {
    const { name, email } = req.body;

    // Check if email is already taken by another user
    if (email && email !== req.user.email) {
      const existingUser = await User.findOne({ email, _id: { $ne: req.user._id } });
      if (existingUser) {
        return next(new AppError('Email already in use', 400, 'البريد الإلكتروني مستخدم بالفعل'));
      }
    }

    const user = await User.findByIdAndUpdate(
      req.user._id,
      { name, email },
      { new: true, runValidators: true }
    );

    sendSuccess(res, 200, {
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        permissions: user.fullPermissions,
        isActive: user.isActive,
        lastLogin: user.lastLogin
      }
    }, 'Profile updated successfully', 'تم تحديث الملف الشخصي بنجاح');
  })
);

// @desc    Change password
// @route   PUT /api/auth/change-password
// @access  Private
router.put('/change-password',
  authenticateToken,
  catchAsync(async (req, res, next) => {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return next(new AppError('Current password and new password are required', 400, 'كلمة المرور الحالية والجديدة مطلوبتان'));
    }

    if (newPassword.length < 6) {
      return next(new AppError('New password must be at least 6 characters', 400, 'كلمة المرور الجديدة يجب أن تكون على الأقل 6 أحرف'));
    }

    // Get user with password
    const user = await User.findById(req.user._id).select('+password');

    // Check current password
    if (!(await user.comparePassword(currentPassword))) {
      return next(new AppError('Current password is incorrect', 400, 'كلمة المرور الحالية غير صحيحة'));
    }

    // Update password
    user.password = newPassword;
    await user.save();

    sendSuccess(res, 200, null, 'Password changed successfully', 'تم تغيير كلمة المرور بنجاح');
  })
);

// @desc    Get all users
// @route   GET /api/auth/users
// @access  Private (Admin only)
router.get('/users',
  authenticateToken,
  requireAdmin,
  catchAsync(async (req, res, next) => {
    const { page = 1, limit = 10, role, isActive, search } = req.query;

    // Build query
    const query = {};
    if (role) query.role = role;
    if (isActive !== undefined) query.isActive = isActive === 'true';
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const users = await User.find(query)
      .populate('createdBy', 'name')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await User.countDocuments(query);

    const usersData = users.map(user => ({
      id: user._id,
      name: user.name,
      email: user.email,
      role: user.role,
      permissions: user.fullPermissions,
      isActive: user.isActive,
      lastLogin: user.lastLogin,
      createdBy: user.createdBy,
      createdAt: user.createdAt
    }));

    res.status(200).json({
      success: true,
      data: usersData,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      },
      message: 'Users retrieved successfully',
      message_ar: 'تم استرداد المستخدمين بنجاح'
    });
  })
);

// @desc    Update user
// @route   PUT /api/auth/users/:id
// @access  Private (Admin only)
router.put('/users/:id',
  authenticateToken,
  requireAdmin,
  catchAsync(async (req, res, next) => {
    const { name, email, role, permissions, isActive } = req.body;
    const userId = req.params.id;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return next(new AppError('User not found', 404, 'المستخدم غير موجود'));
    }

    // Prevent admin from deactivating themselves
    if (userId === req.user._id.toString() && isActive === false) {
      return next(new AppError('Cannot deactivate your own account', 400, 'لا يمكن إلغاء تفعيل حسابك الخاص'));
    }

    // Check if email is already taken
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ email, _id: { $ne: userId } });
      if (existingUser) {
        return next(new AppError('Email already in use', 400, 'البريد الإلكتروني مستخدم بالفعل'));
      }
    }

    // Update user
    const updateData = { name, email, role, isActive };
    if (role === 'staff' && permissions) {
      updateData.permissions = permissions;
    }

    const updatedUser = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, runValidators: true }
    );

    sendSuccess(res, 200, {
      user: {
        id: updatedUser._id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        permissions: updatedUser.fullPermissions,
        isActive: updatedUser.isActive,
        lastLogin: updatedUser.lastLogin
      }
    }, 'User updated successfully', 'تم تحديث المستخدم بنجاح');
  })
);

// @desc    Delete user
// @route   DELETE /api/auth/users/:id
// @access  Private (Admin only)
router.delete('/users/:id',
  authenticateToken,
  requireAdmin,
  catchAsync(async (req, res, next) => {
    const userId = req.params.id;

    // Prevent admin from deleting themselves
    if (userId === req.user._id.toString()) {
      return next(new AppError('Cannot delete your own account', 400, 'لا يمكن حذف حسابك الخاص'));
    }

    const user = await User.findById(userId);
    if (!user) {
      return next(new AppError('User not found', 404, 'المستخدم غير موجود'));
    }

    await User.findByIdAndDelete(userId);

    sendSuccess(res, 200, null, 'User deleted successfully', 'تم حذف المستخدم بنجاح');
  })
);

// @desc    Logout (client-side token invalidation)
// @route   POST /api/auth/logout
// @access  Private
router.post('/logout',
  authenticateToken,
  catchAsync(async (req, res, next) => {
    // In a more sophisticated setup, you might want to blacklist the token
    // For now, we'll just send a success response
    sendSuccess(res, 200, null, 'Logged out successfully', 'تم تسجيل الخروج بنجاح');
  })
);

module.exports = router;
