const mongoose = require('mongoose');

const sessionSchema = new mongoose.Schema({
  sessionId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'SES-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9).toUpperCase();
    }
  },
  console: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Console',
    required: [true, 'الجهاز مطلوب']
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Customer',
    required: false // Optional - can be anonymous session
  },
  customerName: {
    type: String,
    trim: true,
    maxlength: [100, 'اسم العميل يجب أن يكون أقل من 100 حرف']
  },
  startTime: {
    type: Date,
    required: [true, 'وقت البداية مطلوب'],
    default: Date.now
  },
  endTime: {
    type: Date,
    validate: {
      validator: function(value) {
        return !value || value > this.startTime;
      },
      message: 'وقت النهاية يجب أن يكون بعد وقت البداية'
    }
  },
  plannedDuration: {
    type: Number, // in minutes
    min: [1, 'المدة المخططة يجب أن تكون على الأقل دقيقة واحدة'],
    max: [1440, 'المدة المخططة لا يمكن أن تتجاوز 24 ساعة']
  },
  actualDuration: {
    type: Number, // in minutes
    min: [0, 'المدة الفعلية لا يمكن أن تكون سالبة']
  },
  hourlyRate: {
    type: Number,
    required: [true, 'سعر الساعة مطلوب'],
    min: [0, 'سعر الساعة يجب أن يكون أكبر من أو يساوي صفر']
  },
  totalCost: {
    type: Number,
    default: 0,
    min: [0, 'التكلفة الإجمالية لا يمكن أن تكون سالبة']
  },
  discountAmount: {
    type: Number,
    default: 0,
    min: [0, 'مبلغ الخصم لا يمكن أن يكون سالب']
  },
  discountReason: {
    type: String,
    trim: true,
    maxlength: [200, 'سبب الخصم يجب أن يكون أقل من 200 حرف']
  },
  taxAmount: {
    type: Number,
    default: 0,
    min: [0, 'مبلغ الضريبة لا يمكن أن يكون سالب']
  },
  finalAmount: {
    type: Number,
    default: 0,
    min: [0, 'المبلغ النهائي لا يمكن أن يكون سالب']
  },
  paymentMethod: {
    type: String,
    enum: {
      values: ['cash', 'prepaid', 'card', 'mixed'],
      message: 'طريقة الدفع غير صحيحة'
    },
    default: 'cash'
  },
  paymentStatus: {
    type: String,
    enum: {
      values: ['pending', 'paid', 'partial', 'refunded'],
      message: 'حالة الدفع غير صحيحة'
    },
    default: 'pending'
  },
  status: {
    type: String,
    enum: {
      values: ['active', 'completed', 'cancelled', 'paused'],
      message: 'حالة الجلسة غير صحيحة'
    },
    default: 'active'
  },
  pausedTime: {
    type: Number, // Total paused time in minutes
    default: 0,
    min: [0, 'وقت الإيقاف المؤقت لا يمكن أن يكون سالب']
  },
  pauseHistory: [{
    pausedAt: { type: Date, required: true },
    resumedAt: { type: Date },
    reason: { type: String, trim: true, maxlength: 200 }
  }],
  gamesPlayed: [{
    name: { type: String, trim: true, maxlength: 100 },
    startTime: { type: Date },
    endTime: { type: Date },
    duration: { type: Number, min: 0 } // in minutes
  }],
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'الملاحظات يجب أن تكون أقل من 1000 حرف']
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  endedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
sessionSchema.index({ sessionId: 1 });
sessionSchema.index({ console: 1 });
sessionSchema.index({ customer: 1 });
sessionSchema.index({ status: 1 });
sessionSchema.index({ startTime: -1 });
sessionSchema.index({ endTime: -1 });
sessionSchema.index({ paymentStatus: 1 });
sessionSchema.index({ createdAt: -1 });

// Virtual for duration in hours
sessionSchema.virtual('durationInHours').get(function() {
  if (this.actualDuration) {
    return this.actualDuration / 60;
  }
  return 0;
});

// Virtual for current duration (for active sessions)
sessionSchema.virtual('currentDuration').get(function() {
  if (this.status === 'active') {
    const now = new Date();
    const startTime = new Date(this.startTime);
    const diffInMs = now - startTime;
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    return Math.max(0, diffInMinutes - this.pausedTime);
  }
  return this.actualDuration || 0;
});

// Virtual for current cost (for active sessions)
sessionSchema.virtual('currentCost').get(function() {
  const duration = this.currentDuration;
  const hours = duration / 60;
  return Math.round((hours * this.hourlyRate) * 100) / 100;
});

// Virtual for Arabic status
sessionSchema.virtual('statusArabic').get(function() {
  const statusMap = {
    'active': 'نشط',
    'completed': 'مكتمل',
    'cancelled': 'ملغي',
    'paused': 'متوقف مؤقتاً'
  };
  return statusMap[this.status] || this.status;
});

// Virtual for Arabic payment method
sessionSchema.virtual('paymentMethodArabic').get(function() {
  const paymentMap = {
    'cash': 'نقدي',
    'prepaid': 'مدفوع مسبقاً',
    'card': 'بطاقة',
    'mixed': 'مختلط'
  };
  return paymentMap[this.paymentMethod] || this.paymentMethod;
});

// Virtual for Arabic payment status
sessionSchema.virtual('paymentStatusArabic').get(function() {
  const statusMap = {
    'pending': 'في الانتظار',
    'paid': 'مدفوع',
    'partial': 'مدفوع جزئياً',
    'refunded': 'مسترد'
  };
  return statusMap[this.paymentStatus] || this.paymentStatus;
});

// Method to pause session
sessionSchema.methods.pauseSession = async function(reason, userId) {
  if (this.status !== 'active') {
    throw new Error('لا يمكن إيقاف جلسة غير نشطة');
  }

  this.status = 'paused';
  this.pauseHistory.push({
    pausedAt: new Date(),
    reason: reason || 'إيقاف مؤقت'
  });

  await this.save();
  return this;
};

// Method to resume session
sessionSchema.methods.resumeSession = async function(userId) {
  if (this.status !== 'paused') {
    throw new Error('لا يمكن استئناف جلسة غير متوقفة');
  }

  const lastPause = this.pauseHistory[this.pauseHistory.length - 1];
  if (lastPause && !lastPause.resumedAt) {
    lastPause.resumedAt = new Date();
    const pauseDuration = (lastPause.resumedAt - lastPause.pausedAt) / (1000 * 60);
    this.pausedTime += pauseDuration;
  }

  this.status = 'active';
  await this.save();
  return this;
};

// Method to end session
sessionSchema.methods.endSession = async function(userId, paymentMethod = 'cash') {
  if (this.status === 'completed') {
    throw new Error('الجلسة مكتملة بالفعل');
  }

  this.endTime = new Date();
  this.status = 'completed';
  this.endedBy = userId;
  this.paymentMethod = paymentMethod;

  // Calculate actual duration
  const totalDurationMs = this.endTime - this.startTime;
  this.actualDuration = Math.floor(totalDurationMs / (1000 * 60)) - this.pausedTime;

  // Calculate costs
  const hours = this.actualDuration / 60;
  this.totalCost = Math.round((hours * this.hourlyRate) * 100) / 100;
  this.finalAmount = this.totalCost - this.discountAmount + this.taxAmount;

  await this.save();
  return this;
};

// Method to calculate current cost
sessionSchema.methods.calculateCurrentCost = function() {
  const duration = this.currentDuration;
  const hours = duration / 60;
  const cost = hours * this.hourlyRate;
  return Math.round(cost * 100) / 100;
};

// Static method to get active sessions
sessionSchema.statics.getActiveSessions = function() {
  return this.find({ status: { $in: ['active', 'paused'] } })
    .populate('console', 'name consoleId type')
    .populate('customer', 'name phone membershipType')
    .populate('createdBy', 'name')
    .sort({ startTime: -1 });
};

// Static method to get daily statistics
sessionSchema.statics.getDailyStats = async function(date = new Date()) {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);

  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);

  const stats = await this.aggregate([
    {
      $match: {
        startTime: { $gte: startOfDay, $lte: endOfDay },
        status: { $in: ['completed', 'active'] }
      }
    },
    {
      $group: {
        _id: null,
        totalSessions: { $sum: 1 },
        completedSessions: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        activeSessions: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        totalRevenue: { $sum: '$finalAmount' },
        totalHours: { $sum: '$actualDuration' },
        averageSessionDuration: { $avg: '$actualDuration' }
      }
    }
  ]);

  return stats[0] || {
    totalSessions: 0,
    completedSessions: 0,
    activeSessions: 0,
    totalRevenue: 0,
    totalHours: 0,
    averageSessionDuration: 0
  };
};

module.exports = mongoose.model('Session', sessionSchema);
