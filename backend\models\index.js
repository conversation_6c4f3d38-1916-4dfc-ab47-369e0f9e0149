// MongoDB Models Export
// This file exports all Mongoose models for easy importing

const User = require('./User');
const Console = require('./Console');
const Customer = require('./Customer');
const Session = require('./Session');
const Report = require('./Report');
const Settings = require('./Settings');

module.exports = {
  User,
  Console,
  Customer,
  Session,
  Report,
  Settings
};

// Initialize default data when models are loaded
const initializeDefaults = async () => {
  try {
    // Create default admin user
    await User.createDefaultAdmin();
    
    // Initialize default settings
    await Settings.initializeDefaults();
    
    console.log('✅ Default data initialization completed');
    console.log('✅ تم إكمال تهيئة البيانات الافتراضية');
  } catch (error) {
    console.error('❌ Error initializing default data:', error);
    console.error('❌ خطأ في تهيئة البيانات الافتراضية:', error);
  }
};

// Export initialization function
module.exports.initializeDefaults = initializeDefaults;
