<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/playstation-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>نظام إدارة محل البلايستيشن</title>
    
    <!-- Arabic Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Meta tags for Arabic content -->
    <meta name="description" content="نظام إدارة محل البلايستيشن - إدارة الجلسات والعملاء والإيرادات">
    <meta name="keywords" content="بلايستيشن, محل ألعاب, إدارة, جلسات, عملاء">
    <meta name="author" content="PlayStation Shop Management System">
    
    <!-- PWA Meta tags -->
    <meta name="theme-color" content="#1e40af">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="محل البلايستيشن">
    
    <style>
      /* RTL Support and Arabic Font Loading */
      * {
        font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
      }
      
      body {
        direction: rtl;
        text-align: right;
      }
      
      /* Loading screen */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-size: 1.5rem;
        font-weight: 600;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-left: 1rem;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <!-- Loading Screen -->
    <div id="loading">
      <div>جاري التحميل...</div>
      <div class="loading-spinner"></div>
    </div>
    
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
    
    <script>
      // Hide loading screen when app is ready
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.opacity = '0';
            loading.style.transition = 'opacity 0.5s ease-out';
            setTimeout(() => loading.remove(), 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
