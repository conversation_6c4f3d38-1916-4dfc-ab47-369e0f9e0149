import { motion } from 'framer-motion'
import { PlayIcon } from '@heroicons/react/24/outline'

const SessionsPage = () => {
  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-sm p-8 border border-gray-200 text-center"
      >
        <PlayIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h1 className="text-2xl font-bold text-gray-900 mb-2">إدارة الجلسات</h1>
        <p className="text-gray-600 mb-6">
          هذه الصفحة ستحتوي على إدارة جلسات اللعب وتتبع الوقت والتكلفة
        </p>
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <p className="text-green-800 text-sm">
            🚧 هذه الصفحة قيد التطوير وستكون متاحة قريباً
          </p>
        </div>
      </motion.div>
    </div>
  )
}

export default SessionsPage
