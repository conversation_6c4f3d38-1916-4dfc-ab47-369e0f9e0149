import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  ComputerDesktopIcon,
  PlayIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  ClockIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'
import { useAuthStore } from '../../store/authStore'
import { useSettingsStore } from '../../store/settingsStore'
import LoadingSpinner, { CardSkeleton } from '../../components/UI/LoadingSpinner'
import api from '../../utils/api'
import { numberUtils, dateUtils } from '../../utils/helpers'

const DashboardPage = () => {
  const [dashboardData, setDashboardData] = useState(null)
  const [loading, setLoading] = useState(true)
  const { user } = useAuthStore()
  const { formatCurrency, getShopName } = useSettingsStore()

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      const response = await api.get('/reports/analytics/dashboard')
      setDashboardData(response.data.data)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, index) => (
            <CardSkeleton key={index} />
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <CardSkeleton />
          <CardSkeleton />
        </div>
      </div>
    )
  }

  const stats = dashboardData?.summary || {}
  const todayStats = dashboardData?.today || {}
  const activeSessions = dashboardData?.activeSessions?.sessions || []

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-sm p-6 border border-gray-200"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              مرحباً، {user?.name} 👋
            </h1>
            <p className="text-gray-600">
              مرحباً بك في لوحة تحكم {getShopName()}
            </p>
          </div>
          <div className="text-right text-sm text-gray-500">
            <p>{dateUtils.formatDateArabic(new Date())}</p>
            <p>{dateUtils.formatTimeArabic(new Date())}</p>
          </div>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="إجمالي الأجهزة"
          value={stats.totalConsoles || 0}
          icon={ComputerDesktopIcon}
          color="blue"
          delay={0.1}
        />
        <StatCard
          title="الجلسات النشطة"
          value={dashboardData?.activeSessions?.count || 0}
          icon={PlayIcon}
          color="green"
          delay={0.2}
        />
        <StatCard
          title="إجمالي العملاء"
          value={stats.totalCustomers || 0}
          icon={UserGroupIcon}
          color="purple"
          delay={0.3}
        />
        <StatCard
          title="إجمالي الإيرادات"
          value={formatCurrency(stats.totalRevenue || 0)}
          icon={CurrencyDollarIcon}
          color="yellow"
          delay={0.4}
          isAmount
        />
      </div>

      {/* Today's Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-white rounded-lg shadow-sm p-6 border border-gray-200"
      >
        <h2 className="text-lg font-semibold text-gray-900 mb-4">إحصائيات اليوم</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <PlayIcon className="w-6 h-6 text-primary-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">{todayStats.totalSessions || 0}</p>
            <p className="text-sm text-gray-600">جلسات اليوم</p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <CurrencyDollarIcon className="w-6 h-6 text-success-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {formatCurrency(todayStats.totalRevenue || 0)}
            </p>
            <p className="text-sm text-gray-600">إيرادات اليوم</p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <ClockIcon className="w-6 h-6 text-warning-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {numberUtils.round((todayStats.totalHours || 0), 1)}
            </p>
            <p className="text-sm text-gray-600">ساعات اللعب</p>
          </div>
        </div>
      </motion.div>

      {/* Active Sessions and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Active Sessions */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-lg shadow-sm p-6 border border-gray-200"
        >
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">الجلسات النشطة</h2>
            <span className="bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
              {activeSessions.length} نشط
            </span>
          </div>
          
          {activeSessions.length === 0 ? (
            <div className="text-center py-8">
              <PlayIcon className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">لا توجد جلسات نشطة حالياً</p>
            </div>
          ) : (
            <div className="space-y-3">
              {activeSessions.map((session, index) => (
                <div
                  key={session._id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center ml-3">
                      <ComputerDesktopIcon className="w-5 h-5 text-primary-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">
                        {session.console?.name || 'جهاز غير محدد'}
                      </p>
                      <p className="text-sm text-gray-600">
                        {session.customerName || 'عميل غير محدد'}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900">
                      {formatCurrency(session.currentCost || 0)}
                    </p>
                    <p className="text-sm text-gray-600">
                      {dateUtils.formatDuration(session.currentDuration * 60 * 1000)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.7 }}
          className="bg-white rounded-lg shadow-sm p-6 border border-gray-200"
        >
          <h2 className="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h2>
          <div className="space-y-3">
            <QuickActionButton
              title="بدء جلسة جديدة"
              description="ابدأ جلسة لعب جديدة"
              icon={PlayIcon}
              color="primary"
              onClick={() => window.location.href = '/sessions'}
            />
            <QuickActionButton
              title="إدارة الأجهزة"
              description="عرض وإدارة الأجهزة"
              icon={ComputerDesktopIcon}
              color="blue"
              onClick={() => window.location.href = '/consoles'}
            />
            <QuickActionButton
              title="إدارة العملاء"
              description="إضافة وإدارة العملاء"
              icon={UserGroupIcon}
              color="purple"
              onClick={() => window.location.href = '/customers'}
            />
            <QuickActionButton
              title="عرض التقارير"
              description="تقارير الإيرادات والإحصائيات"
              icon={ChartBarIcon}
              color="green"
              onClick={() => window.location.href = '/reports'}
            />
          </div>
        </motion.div>
      </div>
    </div>
  )
}

// Stat Card Component
const StatCard = ({ title, value, icon: Icon, color, delay, isAmount = false }) => {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-600',
    green: 'bg-green-100 text-green-600',
    purple: 'bg-purple-100 text-purple-600',
    yellow: 'bg-yellow-100 text-yellow-600'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay }}
      className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 card-hover"
    >
      <div className="flex items-center">
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${colorClasses[color]}`}>
          <Icon className="w-6 h-6" />
        </div>
        <div className="mr-4 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-2xl font-bold text-gray-900 ${isAmount ? 'text-lg' : ''}`}>
            {value}
          </p>
        </div>
      </div>
    </motion.div>
  )
}

// Quick Action Button Component
const QuickActionButton = ({ title, description, icon: Icon, color, onClick }) => {
  const colorClasses = {
    primary: 'bg-primary-100 text-primary-600 hover:bg-primary-200',
    blue: 'bg-blue-100 text-blue-600 hover:bg-blue-200',
    purple: 'bg-purple-100 text-purple-600 hover:bg-purple-200',
    green: 'bg-green-100 text-green-600 hover:bg-green-200'
  }

  return (
    <button
      onClick={onClick}
      className="w-full flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors text-right"
    >
      <div className={`w-10 h-10 rounded-lg flex items-center justify-center ml-3 ${colorClasses[color]}`}>
        <Icon className="w-5 h-5" />
      </div>
      <div className="flex-1">
        <p className="font-medium text-gray-900">{title}</p>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
    </button>
  )
}

export default DashboardPage
