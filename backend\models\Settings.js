const mongoose = require('mongoose');

const settingsSchema = new mongoose.Schema({
  key: {
    type: String,
    required: [true, 'مفتاح الإعداد مطلوب'],
    unique: true,
    trim: true,
    uppercase: true
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: [true, 'قيمة الإعداد مطلوبة']
  },
  type: {
    type: String,
    required: [true, 'نوع الإعداد مطلوب'],
    enum: {
      values: ['string', 'number', 'boolean', 'object', 'array'],
      message: 'نوع الإعداد غير صحيح'
    }
  },
  category: {
    type: String,
    required: [true, 'فئة الإعداد مطلوبة'],
    enum: {
      values: ['general', 'pricing', 'payment', 'notifications', 'security', 'reports', 'ui'],
      message: 'فئة الإعداد غير صحيحة'
    }
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'وصف الإعداد يجب أن يكون أقل من 500 حرف']
  },
  descriptionArabic: {
    type: String,
    trim: true,
    maxlength: [500, 'الوصف العربي يجب أن يكون أقل من 500 حرف']
  },
  isEditable: {
    type: Boolean,
    default: true
  },
  isVisible: {
    type: Boolean,
    default: true
  },
  validationRules: {
    min: { type: Number },
    max: { type: Number },
    required: { type: Boolean, default: false },
    pattern: { type: String },
    options: [{ type: mongoose.Schema.Types.Mixed }]
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastModifiedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
settingsSchema.index({ key: 1 });
settingsSchema.index({ category: 1 });
settingsSchema.index({ isVisible: 1 });

// Virtual for Arabic category
settingsSchema.virtual('categoryArabic').get(function() {
  const categoryMap = {
    'general': 'عام',
    'pricing': 'التسعير',
    'payment': 'الدفع',
    'notifications': 'الإشعارات',
    'security': 'الأمان',
    'reports': 'التقارير',
    'ui': 'واجهة المستخدم'
  };
  return categoryMap[this.category] || this.category;
});

// Method to update setting value
settingsSchema.methods.updateValue = async function(newValue, userId) {
  // Validate the new value based on type and rules
  if (this.type === 'number' && typeof newValue !== 'number') {
    throw new Error('القيمة يجب أن تكون رقم');
  }
  
  if (this.type === 'boolean' && typeof newValue !== 'boolean') {
    throw new Error('القيمة يجب أن تكون true أو false');
  }
  
  if (this.validationRules.min !== undefined && newValue < this.validationRules.min) {
    throw new Error(`القيمة يجب أن تكون أكبر من أو تساوي ${this.validationRules.min}`);
  }
  
  if (this.validationRules.max !== undefined && newValue > this.validationRules.max) {
    throw new Error(`القيمة يجب أن تكون أقل من أو تساوي ${this.validationRules.max}`);
  }
  
  if (this.validationRules.options && this.validationRules.options.length > 0) {
    if (!this.validationRules.options.includes(newValue)) {
      throw new Error('القيمة غير صحيحة');
    }
  }
  
  this.value = newValue;
  this.lastModifiedBy = userId;
  this.lastModifiedAt = new Date();
  
  await this.save();
  return this;
};

// Static method to get setting by key
settingsSchema.statics.getSetting = async function(key) {
  const setting = await this.findOne({ key: key.toUpperCase() });
  return setting ? setting.value : null;
};

// Static method to set setting value
settingsSchema.statics.setSetting = async function(key, value, userId) {
  const setting = await this.findOne({ key: key.toUpperCase() });
  if (!setting) {
    throw new Error('الإعداد غير موجود');
  }
  
  if (!setting.isEditable) {
    throw new Error('هذا الإعداد غير قابل للتعديل');
  }
  
  return await setting.updateValue(value, userId);
};

// Static method to get all settings by category
settingsSchema.statics.getSettingsByCategory = function(category) {
  return this.find({ 
    category: category, 
    isVisible: true 
  }).sort({ key: 1 });
};

// Static method to initialize default settings
settingsSchema.statics.initializeDefaults = async function() {
  const defaultSettings = [
    // General Settings
    {
      key: 'SHOP_NAME',
      value: 'محل البلايستيشن',
      type: 'string',
      category: 'general',
      description: 'Shop name',
      descriptionArabic: 'اسم المحل'
    },
    {
      key: 'SHOP_ADDRESS',
      value: 'القاهرة، مصر',
      type: 'string',
      category: 'general',
      description: 'Shop address',
      descriptionArabic: 'عنوان المحل'
    },
    {
      key: 'SHOP_PHONE',
      value: '+20 ************',
      type: 'string',
      category: 'general',
      description: 'Shop phone number',
      descriptionArabic: 'رقم هاتف المحل'
    },
    {
      key: 'CURRENCY',
      value: 'EGP',
      type: 'string',
      category: 'general',
      description: 'Currency code',
      descriptionArabic: 'رمز العملة',
      validationRules: { options: ['EGP', 'USD', 'EUR'] }
    },
    {
      key: 'TIMEZONE',
      value: 'Africa/Cairo',
      type: 'string',
      category: 'general',
      description: 'Timezone',
      descriptionArabic: 'المنطقة الزمنية'
    },
    
    // Pricing Settings
    {
      key: 'DEFAULT_HOURLY_RATE',
      value: 25,
      type: 'number',
      category: 'pricing',
      description: 'Default hourly rate in EGP',
      descriptionArabic: 'السعر الافتراضي للساعة بالجنيه المصري',
      validationRules: { min: 1, max: 1000 }
    },
    {
      key: 'TAX_RATE',
      value: 0.14,
      type: 'number',
      category: 'pricing',
      description: 'Tax rate (14% VAT)',
      descriptionArabic: 'معدل الضريبة (14% ضريبة القيمة المضافة)',
      validationRules: { min: 0, max: 1 }
    },
    {
      key: 'MINIMUM_SESSION_DURATION',
      value: 15,
      type: 'number',
      category: 'pricing',
      description: 'Minimum session duration in minutes',
      descriptionArabic: 'الحد الأدنى لمدة الجلسة بالدقائق',
      validationRules: { min: 1, max: 120 }
    },
    {
      key: 'MAXIMUM_SESSION_DURATION',
      value: 480,
      type: 'number',
      category: 'pricing',
      description: 'Maximum session duration in minutes',
      descriptionArabic: 'الحد الأقصى لمدة الجلسة بالدقائق',
      validationRules: { min: 60, max: 1440 }
    },
    
    // Payment Settings
    {
      key: 'ACCEPT_CASH',
      value: true,
      type: 'boolean',
      category: 'payment',
      description: 'Accept cash payments',
      descriptionArabic: 'قبول الدفع النقدي'
    },
    {
      key: 'ACCEPT_CARDS',
      value: true,
      type: 'boolean',
      category: 'payment',
      description: 'Accept card payments',
      descriptionArabic: 'قبول الدفع بالبطاقات'
    },
    {
      key: 'ENABLE_PREPAID',
      value: true,
      type: 'boolean',
      category: 'payment',
      description: 'Enable prepaid balance',
      descriptionArabic: 'تفعيل الرصيد المدفوع مسبقاً'
    },
    
    // Notifications Settings
    {
      key: 'ENABLE_SOUND_NOTIFICATIONS',
      value: true,
      type: 'boolean',
      category: 'notifications',
      description: 'Enable sound notifications',
      descriptionArabic: 'تفعيل الإشعارات الصوتية'
    },
    {
      key: 'SESSION_WARNING_TIME',
      value: 10,
      type: 'number',
      category: 'notifications',
      description: 'Session warning time in minutes before end',
      descriptionArabic: 'وقت تحذير الجلسة بالدقائق قبل الانتهاء',
      validationRules: { min: 1, max: 60 }
    },
    
    // Security Settings
    {
      key: 'SESSION_TIMEOUT',
      value: 30,
      type: 'number',
      category: 'security',
      description: 'User session timeout in minutes',
      descriptionArabic: 'انتهاء جلسة المستخدم بالدقائق',
      validationRules: { min: 5, max: 480 }
    },
    {
      key: 'MAX_LOGIN_ATTEMPTS',
      value: 5,
      type: 'number',
      category: 'security',
      description: 'Maximum login attempts before lockout',
      descriptionArabic: 'الحد الأقصى لمحاولات تسجيل الدخول قبل الحظر',
      validationRules: { min: 3, max: 10 }
    },
    
    // UI Settings
    {
      key: 'THEME',
      value: 'light',
      type: 'string',
      category: 'ui',
      description: 'UI theme',
      descriptionArabic: 'مظهر واجهة المستخدم',
      validationRules: { options: ['light', 'dark', 'auto'] }
    },
    {
      key: 'LANGUAGE',
      value: 'ar',
      type: 'string',
      category: 'ui',
      description: 'Interface language',
      descriptionArabic: 'لغة الواجهة',
      validationRules: { options: ['ar', 'en'] }
    }
  ];
  
  for (const setting of defaultSettings) {
    const exists = await this.findOne({ key: setting.key });
    if (!exists) {
      await this.create(setting);
    }
  }
  
  console.log('✅ Default settings initialized');
  console.log('✅ تم تهيئة الإعدادات الافتراضية');
};

module.exports = mongoose.model('Settings', settingsSchema);
