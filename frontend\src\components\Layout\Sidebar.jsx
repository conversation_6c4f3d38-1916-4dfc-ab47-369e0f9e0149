import { NavLink, useLocation } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  HomeIcon, 
  ComputerDesktopIcon, 
  PlayIcon, 
  UserGroupIcon, 
  ChartBarIcon, 
  CogIcon, 
  UsersIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'
import { usePermissionCheck } from '../Auth/ProtectedRoute'
import { useSettingsStore } from '../../store/settingsStore'

const Sidebar = ({ isOpen, onClose, user }) => {
  const location = useLocation()
  const permissions = usePermissionCheck()
  const { getShopName } = useSettingsStore()

  // Navigation items based on permissions
  const navigationItems = [
    {
      name: 'لوحة التحكم',
      href: '/dashboard',
      icon: HomeIcon,
      show: true
    },
    {
      name: 'الأجهزة',
      href: '/consoles',
      icon: ComputerDesktopIcon,
      show: permissions.canManageConsoles
    },
    {
      name: 'الجلسات',
      href: '/sessions',
      icon: PlayIcon,
      show: permissions.canManageSessions
    },
    {
      name: 'العملاء',
      href: '/customers',
      icon: UserGroupIcon,
      show: permissions.canManageCustomers
    },
    {
      name: 'التقارير',
      href: '/reports',
      icon: ChartBarIcon,
      show: permissions.canViewReports
    },
    {
      name: 'المستخدمين',
      href: '/users',
      icon: UsersIcon,
      show: permissions.canManageUsers
    },
    {
      name: 'الإعدادات',
      href: '/settings',
      icon: CogIcon,
      show: permissions.canManageSettings
    }
  ]

  const visibleItems = navigationItems.filter(item => item.show)

  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      x: "100%",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  }

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:right-0 lg:z-50 lg:block lg:w-64 lg:overflow-y-auto lg:bg-white lg:shadow-xl">
        <SidebarContent 
          items={visibleItems} 
          user={user} 
          shopName={getShopName()}
          currentPath={location.pathname}
        />
      </div>

      {/* Mobile Sidebar */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial="closed"
            animate="open"
            exit="closed"
            variants={sidebarVariants}
            className="fixed inset-y-0 right-0 z-50 w-64 overflow-y-auto bg-white shadow-xl lg:hidden"
          >
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold text-gray-800">
                {getShopName()}
              </h2>
              <button
                onClick={onClose}
                className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors"
              >
                <XMarkIcon className="w-6 h-6" />
              </button>
            </div>
            
            <SidebarContent 
              items={visibleItems} 
              user={user} 
              shopName={getShopName()}
              currentPath={location.pathname}
              onItemClick={onClose}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

// Sidebar content component
const SidebarContent = ({ items, user, shopName, currentPath, onItemClick }) => {
  return (
    <div className="flex flex-col h-full">
      {/* Logo/Brand */}
      <div className="flex items-center px-6 py-4 border-b border-gray-200">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">🎮</span>
          </div>
          <div className="mr-3">
            <h1 className="text-lg font-bold text-gray-800 gradient-text">
              {shopName}
            </h1>
            <p className="text-xs text-gray-500">نظام إدارة المحل</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {items.map((item) => {
          const isActive = currentPath === item.href
          const Icon = item.icon

          return (
            <NavLink
              key={item.name}
              to={item.href}
              onClick={onItemClick}
              className={({ isActive }) => `
                group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200
                ${isActive 
                  ? 'bg-primary-100 text-primary-700 border-r-4 border-primary-600' 
                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                }
              `}
            >
              <Icon className={`
                ml-3 h-5 w-5 transition-colors duration-200
                ${isActive ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-600'}
              `} />
              <span>{item.name}</span>
              
              {/* Active indicator */}
              {isActive && (
                <motion.div
                  layoutId="activeTab"
                  className="absolute right-0 w-1 h-8 bg-primary-600 rounded-l-full"
                  initial={false}
                  transition={{
                    type: "spring",
                    stiffness: 500,
                    damping: 30
                  }}
                />
              )}
            </NavLink>
          )
        })}
      </nav>

      {/* User info */}
      <div className="border-t border-gray-200 p-4">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
            <span className="text-white font-medium text-sm">
              {user?.name?.charAt(0) || 'U'}
            </span>
          </div>
          <div className="mr-3 flex-1">
            <p className="text-sm font-medium text-gray-800">
              {user?.name || 'مستخدم'}
            </p>
            <p className="text-xs text-gray-500">
              {user?.role === 'admin' ? 'مدير' : 'موظف'}
            </p>
          </div>
        </div>
        
        {/* Status indicator */}
        <div className="mt-3 flex items-center text-xs text-gray-500">
          <div className="w-2 h-2 bg-success-500 rounded-full ml-2 animate-pulse"></div>
          متصل
        </div>
      </div>
    </div>
  )
}

export default Sidebar
