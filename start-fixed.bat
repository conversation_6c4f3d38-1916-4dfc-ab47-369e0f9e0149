@echo off
chcp 65001 >nul
echo ========================================
echo    PlayStation Shop Management System
echo    نظام إدارة محل البلايستيشن
echo ========================================
echo.

echo [التحقق من المتطلبات / Checking Requirements...]
echo.

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت / Node.js not installed
    echo يرجى تحميل وتثبيت Node.js من / Please download and install Node.js from:
    echo https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js مثبت / Node.js installed

REM Check directories
if not exist "backend" (
    echo ❌ مجلد backend غير موجود / backend directory missing
    pause
    exit /b 1
)
if not exist "frontend" (
    echo ❌ مجلد frontend غير موجود / frontend directory missing
    pause
    exit /b 1
)

echo.
echo [إعداد ملفات البيئة / Setting up environment files...]

REM Create backend .env if missing
if not exist "backend\.env" (
    echo إنشاء backend/.env / Creating backend/.env...
    (
        echo NODE_ENV=development
        echo PORT=5000
        echo MONGODB_URI=mongodb://localhost:27017/playstation-shop
        echo JWT_SECRET=your-super-secret-jwt-key-2024-playstation
        echo JWT_EXPIRES_IN=7d
        echo DEFAULT_ADMIN_EMAIL=<EMAIL>
        echo DEFAULT_ADMIN_PASSWORD=admin123456
        echo FRONTEND_URL=http://localhost:3000
    ) > "backend\.env"
    echo ✅ تم إنشاء backend/.env / backend/.env created
)

REM Create frontend .env if missing
if not exist "frontend\.env" (
    echo إنشاء frontend/.env / Creating frontend/.env...
    (
        echo VITE_API_URL=http://localhost:5000/api
        echo VITE_APP_NAME=PlayStation Shop Management
        echo VITE_APP_VERSION=1.0.0
    ) > "frontend\.env"
    echo ✅ تم إنشاء frontend/.env / frontend/.env created
)

echo.
echo [تثبيت التبعيات / Installing Dependencies...]

REM Install backend dependencies
echo تثبيت تبعيات Backend / Installing Backend dependencies...
cd backend
if not exist "node_modules" (
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت تبعيات Backend / Failed to install Backend dependencies
        echo جرب الأوامر التالية يدوياً / Try these commands manually:
        echo cd backend
        echo npm install
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت تبعيات Backend / Backend dependencies installed
) else (
    echo ✅ تبعيات Backend مثبتة مسبقاً / Backend dependencies already installed
)

REM Install frontend dependencies
echo تثبيت تبعيات Frontend / Installing Frontend dependencies...
cd ..\frontend
if not exist "node_modules" (
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت تبعيات Frontend / Failed to install Frontend dependencies
        echo جرب الأوامر التالية يدوياً / Try these commands manually:
        echo cd frontend
        echo npm install
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت تبعيات Frontend / Frontend dependencies installed
) else (
    echo ✅ تبعيات Frontend مثبتة مسبقاً / Frontend dependencies already installed
)

echo.
echo [بدء تشغيل الخوادم / Starting Servers...]

REM Kill any existing processes on ports 3000 and 5000
echo إيقاف العمليات السابقة / Stopping previous processes...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":5000" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| find ":3000" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1

REM Start backend
echo بدء Backend Server / Starting Backend Server...
cd ..\backend
start "PlayStation Shop - Backend Server" cmd /k "echo Backend Server بدأ التشغيل / Starting... && npm run dev"

REM Wait for backend to start
echo انتظار بدء Backend / Waiting for Backend to start...
timeout /t 5 /nobreak >nul

REM Test backend connection
echo اختبار الاتصال بـ Backend / Testing Backend connection...
curl -s http://localhost:5000/api >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Backend قد لا يكون جاهزاً بعد / Backend may not be ready yet
    echo سيتم المحاولة مرة أخرى / Will try again...
    timeout /t 3 /nobreak >nul
)

REM Start frontend
echo بدء Frontend Server / Starting Frontend Server...
cd ..\frontend
start "PlayStation Shop - Frontend Server" cmd /k "echo Frontend Server بدأ التشغيل / Starting... && npm run dev"

echo.
echo ========================================
echo ✅ تم بدء تشغيل النظام! / System Started!
echo ========================================
echo.
echo 🌐 الروابط / URLs:
echo   Frontend: http://localhost:3000
echo   Backend:  http://localhost:5000/api
echo.
echo 🔑 بيانات تسجيل الدخول / Login Credentials:
echo   Email:    <EMAIL>
echo   Password: admin123456
echo.
echo 📝 ملاحظات مهمة / Important Notes:
echo   - انتظر 30-60 ثانية حتى يكتمل التحميل / Wait 30-60 seconds for full loading
echo   - إذا ظهر خطأ اتصال، تأكد من تشغيل Backend أولاً / If connection error, ensure Backend starts first
echo   - يمكنك إغلاق هذه النافذة بأمان / You can safely close this window
echo.
echo ========================================

REM Wait a bit more and try to open browser
timeout /t 10 /nobreak >nul
echo فتح المتصفح / Opening browser...
start http://localhost:3000

echo.
echo اضغط أي مفتاح للخروج / Press any key to exit...
pause >nul
