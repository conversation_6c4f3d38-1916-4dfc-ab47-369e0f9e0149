const mongoose = require('mongoose');

const reportSchema = new mongoose.Schema({
  reportId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'RPT-' + Date.now() + '-' + Math.random().toString(36).substr(2, 6).toUpperCase();
    }
  },
  title: {
    type: String,
    required: [true, 'عنوان التقرير مطلوب'],
    trim: true,
    maxlength: [200, 'عنوان التقرير يجب أن يكون أقل من 200 حرف']
  },
  type: {
    type: String,
    required: [true, 'نوع التقرير مطلوب'],
    enum: {
      values: ['daily', 'weekly', 'monthly', 'yearly', 'custom', 'console_performance', 'customer_analysis', 'revenue_analysis'],
      message: 'نوع التقرير غير صحيح'
    }
  },
  dateRange: {
    startDate: {
      type: Date,
      required: [true, 'تاريخ البداية مطلوب']
    },
    endDate: {
      type: Date,
      required: [true, 'تاريخ النهاية مطلوب'],
      validate: {
        validator: function(value) {
          return value >= this.dateRange.startDate;
        },
        message: 'تاريخ النهاية يجب أن يكون بعد أو يساوي تاريخ البداية'
      }
    }
  },
  filters: {
    consoles: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Console' }],
    customers: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Customer' }],
    paymentMethods: [{ type: String, enum: ['cash', 'prepaid', 'card', 'mixed'] }],
    sessionStatus: [{ type: String, enum: ['active', 'completed', 'cancelled', 'paused'] }]
  },
  data: {
    // Revenue Statistics
    revenue: {
      total: { type: Number, default: 0 },
      cash: { type: Number, default: 0 },
      prepaid: { type: Number, default: 0 },
      card: { type: Number, default: 0 },
      mixed: { type: Number, default: 0 },
      discounts: { type: Number, default: 0 },
      taxes: { type: Number, default: 0 }
    },
    
    // Session Statistics
    sessions: {
      total: { type: Number, default: 0 },
      completed: { type: Number, default: 0 },
      active: { type: Number, default: 0 },
      cancelled: { type: Number, default: 0 },
      averageDuration: { type: Number, default: 0 },
      totalHours: { type: Number, default: 0 }
    },
    
    // Console Statistics
    consoles: {
      total: { type: Number, default: 0 },
      active: { type: Number, default: 0 },
      available: { type: Number, default: 0 },
      maintenance: { type: Number, default: 0 },
      utilizationRate: { type: Number, default: 0 },
      topPerforming: [{
        console: { type: mongoose.Schema.Types.ObjectId, ref: 'Console' },
        sessions: { type: Number, default: 0 },
        revenue: { type: Number, default: 0 },
        hours: { type: Number, default: 0 }
      }]
    },
    
    // Customer Statistics
    customers: {
      total: { type: Number, default: 0 },
      new: { type: Number, default: 0 },
      returning: { type: Number, default: 0 },
      vip: { type: Number, default: 0 },
      premium: { type: Number, default: 0 },
      topSpenders: [{
        customer: { type: mongoose.Schema.Types.ObjectId, ref: 'Customer' },
        sessions: { type: Number, default: 0 },
        totalSpent: { type: Number, default: 0 },
        hours: { type: Number, default: 0 }
      }]
    },
    
    // Time-based Analytics
    timeAnalytics: {
      peakHours: [{
        hour: { type: Number, min: 0, max: 23 },
        sessions: { type: Number, default: 0 },
        revenue: { type: Number, default: 0 }
      }],
      dailyBreakdown: [{
        date: { type: Date },
        sessions: { type: Number, default: 0 },
        revenue: { type: Number, default: 0 },
        hours: { type: Number, default: 0 }
      }],
      weeklyTrends: [{
        week: { type: Number, min: 1, max: 53 },
        year: { type: Number },
        sessions: { type: Number, default: 0 },
        revenue: { type: Number, default: 0 }
      }]
    }
  },
  
  // Report Metadata
  generatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  generatedAt: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: {
      values: ['generating', 'completed', 'failed'],
      message: 'حالة التقرير غير صحيحة'
    },
    default: 'generating'
  },
  format: {
    type: String,
    enum: {
      values: ['json', 'pdf', 'excel', 'csv'],
      message: 'تنسيق التقرير غير صحيح'
    },
    default: 'json'
  },
  filePath: {
    type: String,
    trim: true
  },
  fileSize: {
    type: Number,
    min: 0
  },
  downloadCount: {
    type: Number,
    default: 0,
    min: 0
  },
  isScheduled: {
    type: Boolean,
    default: false
  },
  scheduleConfig: {
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly'],
      required: function() { return this.isScheduled; }
    },
    nextRun: {
      type: Date,
      required: function() { return this.isScheduled; }
    },
    recipients: [{
      email: { type: String, trim: true, lowercase: true },
      name: { type: String, trim: true }
    }]
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'الملاحظات يجب أن تكون أقل من 1000 حرف']
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
reportSchema.index({ reportId: 1 });
reportSchema.index({ type: 1 });
reportSchema.index({ 'dateRange.startDate': -1 });
reportSchema.index({ 'dateRange.endDate': -1 });
reportSchema.index({ generatedBy: 1 });
reportSchema.index({ generatedAt: -1 });
reportSchema.index({ status: 1 });
reportSchema.index({ isScheduled: 1 });

// Virtual for Arabic report type
reportSchema.virtual('typeArabic').get(function() {
  const typeMap = {
    'daily': 'يومي',
    'weekly': 'أسبوعي',
    'monthly': 'شهري',
    'yearly': 'سنوي',
    'custom': 'مخصص',
    'console_performance': 'أداء الأجهزة',
    'customer_analysis': 'تحليل العملاء',
    'revenue_analysis': 'تحليل الإيرادات'
  };
  return typeMap[this.type] || this.type;
});

// Virtual for Arabic status
reportSchema.virtual('statusArabic').get(function() {
  const statusMap = {
    'generating': 'جاري الإنشاء',
    'completed': 'مكتمل',
    'failed': 'فشل'
  };
  return statusMap[this.status] || this.status;
});

// Virtual for date range in Arabic
reportSchema.virtual('dateRangeArabic').get(function() {
  const startDate = this.dateRange.startDate.toLocaleDateString('ar-EG');
  const endDate = this.dateRange.endDate.toLocaleDateString('ar-EG');
  return `من ${startDate} إلى ${endDate}`;
});

// Method to mark report as completed
reportSchema.methods.markCompleted = async function(filePath, fileSize) {
  this.status = 'completed';
  this.filePath = filePath;
  this.fileSize = fileSize;
  await this.save();
  return this;
};

// Method to mark report as failed
reportSchema.methods.markFailed = async function(error) {
  this.status = 'failed';
  this.notes = error || 'فشل في إنشاء التقرير';
  await this.save();
  return this;
};

// Method to increment download count
reportSchema.methods.incrementDownload = async function() {
  this.downloadCount += 1;
  await this.save();
  return this.downloadCount;
};

// Static method to get recent reports
reportSchema.statics.getRecentReports = function(limit = 10) {
  return this.find({ status: 'completed' })
    .populate('generatedBy', 'name')
    .sort({ generatedAt: -1 })
    .limit(limit)
    .select('reportId title type dateRange generatedAt downloadCount');
};

// Static method to clean old reports
reportSchema.statics.cleanOldReports = async function(daysOld = 30) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysOld);
  
  const result = await this.deleteMany({
    generatedAt: { $lt: cutoffDate },
    isScheduled: false
  });
  
  return result.deletedCount;
};

module.exports = mongoose.model('Report', reportSchema);
