import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Bars3Icon, 
  BellIcon, 
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  CogIcon,
  UserIcon
} from '@heroicons/react/24/outline'
import { useAuthStore } from '../../store/authStore'
import { useSettingsStore } from '../../store/settingsStore'
import { dateUtils } from '../../utils/helpers'

const Header = ({ onMenuClick, user }) => {
  const [profileMenuOpen, setProfileMenuOpen] = useState(false)
  const [notificationsOpen, setNotificationsOpen] = useState(false)
  const profileMenuRef = useRef(null)
  const notificationsRef = useRef(null)
  
  const { logout } = useAuthStore()
  const { formatTime, formatDate } = useSettingsStore()

  // Close menus when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (profileMenuRef.current && !profileMenuRef.current.contains(event.target)) {
        setProfileMenuOpen(false)
      }
      if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {
        setNotificationsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleLogout = async () => {
    await logout()
    setProfileMenuOpen(false)
  }

  const currentTime = formatTime(new Date())
  const currentDate = formatDate(new Date())

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Mobile menu button */}
          <div className="flex items-center lg:hidden">
            <button
              onClick={onMenuClick}
              className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors"
            >
              <Bars3Icon className="w-6 h-6" />
            </button>
          </div>

          {/* Date and time */}
          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            <div className="text-right">
              <p className="text-sm font-medium text-gray-800">
                {currentDate}
              </p>
              <p className="text-xs text-gray-500">
                {currentTime}
              </p>
            </div>
          </div>

          {/* Right side - Notifications and Profile */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {/* Notifications */}
            <div className="relative" ref={notificationsRef}>
              <button
                onClick={() => setNotificationsOpen(!notificationsOpen)}
                className="p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors relative"
              >
                <BellIcon className="w-6 h-6" />
                {/* Notification badge */}
                <span className="absolute -top-1 -right-1 w-4 h-4 bg-danger-500 text-white text-xs rounded-full flex items-center justify-center">
                  3
                </span>
              </button>

              {/* Notifications dropdown */}
              <AnimatePresence>
                {notificationsOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                    className="absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
                  >
                    <div className="px-4 py-2 border-b border-gray-100">
                      <h3 className="text-sm font-medium text-gray-800">الإشعارات</h3>
                    </div>
                    
                    <div className="max-h-64 overflow-y-auto">
                      {/* Sample notifications */}
                      <div className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50">
                        <div className="flex items-start">
                          <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 ml-3"></div>
                          <div className="flex-1">
                            <p className="text-sm text-gray-800">جلسة جديدة بدأت على جهاز PS5-001</p>
                            <p className="text-xs text-gray-500 mt-1">منذ 5 دقائق</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50">
                        <div className="flex items-start">
                          <div className="w-2 h-2 bg-warning-500 rounded-full mt-2 ml-3"></div>
                          <div className="flex-1">
                            <p className="text-sm text-gray-800">تحذير: جلسة ستنتهي خلال 10 دقائق</p>
                            <p className="text-xs text-gray-500 mt-1">منذ دقيقتين</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="px-4 py-3 hover:bg-gray-50 cursor-pointer">
                        <div className="flex items-start">
                          <div className="w-2 h-2 bg-success-500 rounded-full mt-2 ml-3"></div>
                          <div className="flex-1">
                            <p className="text-sm text-gray-800">تم إنهاء جلسة بنجاح - 45 جنيه</p>
                            <p className="text-xs text-gray-500 mt-1">منذ 15 دقيقة</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="px-4 py-2 border-t border-gray-100">
                      <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
                        عرض جميع الإشعارات
                      </button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Profile dropdown */}
            <div className="relative" ref={profileMenuRef}>
              <button
                onClick={() => setProfileMenuOpen(!profileMenuOpen)}
                className="flex items-center space-x-2 space-x-reverse p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-medium text-sm">
                    {user?.name?.charAt(0) || 'U'}
                  </span>
                </div>
                <div className="hidden md:block text-right">
                  <p className="text-sm font-medium text-gray-800">
                    {user?.name || 'مستخدم'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {user?.role === 'admin' ? 'مدير' : 'موظف'}
                  </p>
                </div>
              </button>

              {/* Profile dropdown menu */}
              <AnimatePresence>
                {profileMenuOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                    className="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
                  >
                    <div className="px-4 py-2 border-b border-gray-100">
                      <p className="text-sm font-medium text-gray-800">{user?.name}</p>
                      <p className="text-xs text-gray-500">{user?.email}</p>
                    </div>
                    
                    <div className="py-1">
                      <button
                        onClick={() => {
                          setProfileMenuOpen(false)
                          // Navigate to profile settings
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      >
                        <UserIcon className="w-4 h-4 ml-3" />
                        الملف الشخصي
                      </button>
                      
                      <button
                        onClick={() => {
                          setProfileMenuOpen(false)
                          // Navigate to settings
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      >
                        <CogIcon className="w-4 h-4 ml-3" />
                        الإعدادات
                      </button>
                    </div>
                    
                    <div className="border-t border-gray-100 py-1">
                      <button
                        onClick={handleLogout}
                        className="flex items-center w-full px-4 py-2 text-sm text-danger-600 hover:bg-danger-50 transition-colors"
                      >
                        <ArrowRightOnRectangleIcon className="w-4 h-4 ml-3" />
                        تسجيل الخروج
                      </button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
