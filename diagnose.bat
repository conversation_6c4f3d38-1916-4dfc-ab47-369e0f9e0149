@echo off
echo ========================================
echo    تشخيص مشاكل الاتصال
echo    Connection Troubleshooting
echo ========================================
echo.

echo [1] Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت / Node.js not installed
    echo يرجى تحميل Node.js من / Please download Node.js from:
    echo https://nodejs.org/
    pause
    exit /b 1
) else (
    echo ✅ Node.js مثبت / Node.js installed
)

echo.
echo [2] Checking npm...
npm --version
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح / npm not available
    pause
    exit /b 1
) else (
    echo ✅ npm متاح / npm available
)

echo.
echo [3] Checking backend directory...
if exist "backend" (
    echo ✅ مجلد backend موجود / backend directory exists
) else (
    echo ❌ مجلد backend غير موجود / backend directory missing
    pause
    exit /b 1
)

echo.
echo [4] Checking frontend directory...
if exist "frontend" (
    echo ✅ مجلد frontend موجود / frontend directory exists
) else (
    echo ❌ مجلد frontend غير موجود / frontend directory missing
    pause
    exit /b 1
)

echo.
echo [5] Checking backend package.json...
if exist "backend\package.json" (
    echo ✅ backend package.json موجود / exists
) else (
    echo ❌ backend package.json مفقود / missing
    pause
    exit /b 1
)

echo.
echo [6] Checking frontend package.json...
if exist "frontend\package.json" (
    echo ✅ frontend package.json موجود / exists
) else (
    echo ❌ frontend package.json مفقود / missing
    pause
    exit /b 1
)

echo.
echo [7] Checking backend .env file...
if exist "backend\.env" (
    echo ✅ backend .env موجود / exists
    echo محتويات الملف / File contents:
    type "backend\.env"
) else (
    echo ❌ backend .env مفقود / missing
    echo إنشاء ملف .env / Creating .env file...
    echo NODE_ENV=development > "backend\.env"
    echo PORT=5000 >> "backend\.env"
    echo MONGODB_URI=mongodb://localhost:27017/playstation-shop >> "backend\.env"
    echo JWT_SECRET=your-super-secret-jwt-key-2024 >> "backend\.env"
    echo JWT_EXPIRES_IN=7d >> "backend\.env"
    echo DEFAULT_ADMIN_EMAIL=<EMAIL> >> "backend\.env"
    echo DEFAULT_ADMIN_PASSWORD=admin123456 >> "backend\.env"
    echo FRONTEND_URL=http://localhost:3000 >> "backend\.env"
    echo ✅ تم إنشاء ملف .env / .env file created
)

echo.
echo [8] Checking frontend .env file...
if exist "frontend\.env" (
    echo ✅ frontend .env موجود / exists
    echo محتويات الملف / File contents:
    type "frontend\.env"
) else (
    echo ❌ frontend .env مفقود / missing
    echo إنشاء ملف .env / Creating .env file...
    echo VITE_API_URL=http://localhost:5000/api > "frontend\.env"
    echo VITE_APP_NAME=PlayStation Shop Management >> "frontend\.env"
    echo VITE_APP_VERSION=1.0.0 >> "frontend\.env"
    echo ✅ تم إنشاء ملف .env / .env file created
)

echo.
echo [9] Testing port 5000 availability...
netstat -an | find "5000" > nul
if %errorlevel% equ 0 (
    echo ⚠️ المنفذ 5000 مستخدم / Port 5000 is in use
    echo قد تحتاج لإيقاف العملية المستخدمة للمنفذ / You may need to stop the process using this port
) else (
    echo ✅ المنفذ 5000 متاح / Port 5000 is available
)

echo.
echo [10] Testing port 3000 availability...
netstat -an | find "3000" > nul
if %errorlevel% equ 0 (
    echo ⚠️ المنفذ 3000 مستخدم / Port 3000 is in use
    echo قد تحتاج لإيقاف العملية المستخدمة للمنفذ / You may need to stop the process using this port
) else (
    echo ✅ المنفذ 3000 متاح / Port 3000 is available
)

echo.
echo ========================================
echo التشخيص مكتمل / Diagnosis Complete
echo ========================================
echo.
pause
