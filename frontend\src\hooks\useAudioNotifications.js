import { useCallback, useRef, useEffect } from 'react'
import { useSettingsStore } from '../store/settingsStore'

// Audio notification types
const AUDIO_TYPES = {
  SESSION_START: 'session_start',
  SESSION_END: 'session_end',
  SESSION_WARNING: 'session_warning',
  PAYMENT_SUCCESS: 'payment_success',
  ERROR: 'error',
  SUCCESS: 'success',
  NOTIFICATION: 'notification'
}

// Default audio URLs (you can replace these with actual audio files)
const DEFAULT_AUDIO_URLS = {
  [AUDIO_TYPES.SESSION_START]: '/sounds/session-start.mp3',
  [AUDIO_TYPES.SESSION_END]: '/sounds/session-end.mp3',
  [AUDIO_TYPES.SESSION_WARNING]: '/sounds/session-warning.mp3',
  [AUDIO_TYPES.PAYMENT_SUCCESS]: '/sounds/payment-success.mp3',
  [AUDIO_TYPES.ERROR]: '/sounds/error.mp3',
  [AUDIO_TYPES.SUCCESS]: '/sounds/success.mp3',
  [AUDIO_TYPES.NOTIFICATION]: '/sounds/notification.mp3'
}

// Fallback audio generation using Web Audio API
const generateTone = (frequency, duration, type = 'sine') => {
  const audioContext = new (window.AudioContext || window.webkitAudioContext)()
  const oscillator = audioContext.createOscillator()
  const gainNode = audioContext.createGain()

  oscillator.connect(gainNode)
  gainNode.connect(audioContext.destination)

  oscillator.frequency.value = frequency
  oscillator.type = type

  gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
  gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration)

  oscillator.start(audioContext.currentTime)
  oscillator.stop(audioContext.currentTime + duration)

  return new Promise(resolve => {
    setTimeout(resolve, duration * 1000)
  })
}

// Fallback sounds using Web Audio API
const FALLBACK_SOUNDS = {
  [AUDIO_TYPES.SESSION_START]: () => generateTone(800, 0.2),
  [AUDIO_TYPES.SESSION_END]: () => generateTone(400, 0.3),
  [AUDIO_TYPES.SESSION_WARNING]: async () => {
    await generateTone(600, 0.1)
    await generateTone(600, 0.1)
    await generateTone(600, 0.1)
  },
  [AUDIO_TYPES.PAYMENT_SUCCESS]: async () => {
    await generateTone(523, 0.1) // C
    await generateTone(659, 0.1) // E
    await generateTone(784, 0.2) // G
  },
  [AUDIO_TYPES.ERROR]: async () => {
    await generateTone(300, 0.1)
    await generateTone(250, 0.2)
  },
  [AUDIO_TYPES.SUCCESS]: () => generateTone(800, 0.15),
  [AUDIO_TYPES.NOTIFICATION]: () => generateTone(600, 0.1)
}

export const useAudioNotifications = () => {
  const audioCache = useRef(new Map())
  const isInitialized = useRef(false)
  const { soundNotificationsEnabled } = useSettingsStore()

  // Initialize audio system
  const initializeAudio = useCallback(() => {
    if (isInitialized.current) return

    // Preload audio files
    Object.entries(DEFAULT_AUDIO_URLS).forEach(([type, url]) => {
      const audio = new Audio()
      audio.preload = 'auto'
      audio.src = url
      
      // Handle load error - fallback to generated tones
      audio.onerror = () => {
        console.warn(`Failed to load audio file: ${url}. Using fallback tone.`)
        audioCache.current.set(type, null) // Mark as failed
      }
      
      audio.oncanplaythrough = () => {
        audioCache.current.set(type, audio)
      }
    })

    isInitialized.current = true
  }, [])

  // Play audio notification
  const playAudio = useCallback(async (type, volume = 0.7) => {
    if (!soundNotificationsEnabled()) {
      return
    }

    try {
      const cachedAudio = audioCache.current.get(type)
      
      if (cachedAudio) {
        // Use cached audio file
        cachedAudio.volume = volume
        cachedAudio.currentTime = 0
        await cachedAudio.play()
      } else {
        // Use fallback generated tone
        const fallbackSound = FALLBACK_SOUNDS[type]
        if (fallbackSound) {
          await fallbackSound()
        }
      }
    } catch (error) {
      console.warn('Audio playback failed:', error)
      
      // Try fallback sound if main audio fails
      try {
        const fallbackSound = FALLBACK_SOUNDS[type]
        if (fallbackSound) {
          await fallbackSound()
        }
      } catch (fallbackError) {
        console.warn('Fallback audio also failed:', fallbackError)
      }
    }
  }, [soundNotificationsEnabled])

  // Specific notification functions
  const playSessionStart = useCallback(() => {
    playAudio(AUDIO_TYPES.SESSION_START)
  }, [playAudio])

  const playSessionEnd = useCallback(() => {
    playAudio(AUDIO_TYPES.SESSION_END)
  }, [playAudio])

  const playSessionWarning = useCallback(() => {
    playAudio(AUDIO_TYPES.SESSION_WARNING)
  }, [playAudio])

  const playPaymentSuccess = useCallback(() => {
    playAudio(AUDIO_TYPES.PAYMENT_SUCCESS)
  }, [playAudio])

  const playError = useCallback(() => {
    playAudio(AUDIO_TYPES.ERROR)
  }, [playAudio])

  const playSuccess = useCallback(() => {
    playAudio(AUDIO_TYPES.SUCCESS)
  }, [playAudio])

  const playNotification = useCallback(() => {
    playAudio(AUDIO_TYPES.NOTIFICATION)
  }, [playAudio])

  // Test audio function
  const testAudio = useCallback(async (type) => {
    await playAudio(type, 0.5)
  }, [playAudio])

  // Get available audio types
  const getAudioTypes = useCallback(() => {
    return Object.values(AUDIO_TYPES)
  }, [])

  // Check if audio is supported
  const isAudioSupported = useCallback(() => {
    return !!(window.AudioContext || window.webkitAudioContext || window.Audio)
  }, [])

  // Set volume for all cached audio
  const setGlobalVolume = useCallback((volume) => {
    audioCache.current.forEach((audio) => {
      if (audio && audio.volume !== undefined) {
        audio.volume = Math.max(0, Math.min(1, volume))
      }
    })
  }, [])

  // Cleanup function
  const cleanup = useCallback(() => {
    audioCache.current.forEach((audio) => {
      if (audio && typeof audio.pause === 'function') {
        audio.pause()
        audio.src = ''
      }
    })
    audioCache.current.clear()
    isInitialized.current = false
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return cleanup
  }, [cleanup])

  return {
    // Initialization
    initializeAudio,
    
    // Generic play function
    playAudio,
    
    // Specific notification functions
    playSessionStart,
    playSessionEnd,
    playSessionWarning,
    playPaymentSuccess,
    playError,
    playSuccess,
    playNotification,
    
    // Utility functions
    testAudio,
    getAudioTypes,
    isAudioSupported,
    setGlobalVolume,
    cleanup,
    
    // Audio types constant
    AUDIO_TYPES
  }
}

// Export audio types for use in other components
export { AUDIO_TYPES }
