import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import toast from 'react-hot-toast'
import api from '../utils/api'

const useSettingsStore = create(
  persist(
    (set, get) => ({
      // State
      settings: {},
      isLoading: false,
      lastUpdated: null,

      // Public settings (available without authentication)
      publicSettings: {
        SHOP_NAME: 'محل البلايستيشن',
        CURRENCY: 'EGP',
        TIMEZONE: 'Africa/Cairo',
        ENABLE_SOUND_NOTIFICATIONS: true,
        THEME: 'light',
        LANGUAGE: 'ar',
        ACCEPT_CASH: true,
        ACCEPT_CARDS: true,
        ENABLE_PREPAID: true
      },

      // Actions
      loadSettings: async () => {
        set({ isLoading: true })

        try {
          // Load public settings first (no auth required)
          const publicResponse = await api.get('/settings/public/config')
          const publicSettings = publicResponse.data.data.config

          set({
            publicSettings,
            isLoading: false,
            lastUpdated: new Date().toISOString()
          })

          // Try to load full settings if authenticated
          try {
            const fullResponse = await api.get('/settings')
            const fullSettings = fullResponse.data.data.settings

            set({
              settings: fullSettings,
              isLoading: false,
              lastUpdated: new Date().toISOString()
            })
          } catch (authError) {
            // User not authenticated, use public settings only
            console.log('Using public settings only')
          }

          return { success: true }
        } catch (error) {
          const message = error.response?.data?.message_ar || 'فشل في تحميل الإعدادات'
          console.error('Settings load error:', message)
          
          set({ isLoading: false })
          return { success: false, error: message }
        }
      },

      loadSettingsByCategory: async (category) => {
        set({ isLoading: true })

        try {
          const response = await api.get(`/settings/category/${category}`)
          const categorySettings = response.data.data.settings

          // Update settings with category data
          const currentSettings = get().settings
          set({
            settings: {
              ...currentSettings,
              [category]: categorySettings
            },
            isLoading: false,
            lastUpdated: new Date().toISOString()
          })

          return { success: true, settings: categorySettings }
        } catch (error) {
          const message = error.response?.data?.message_ar || 'فشل في تحميل إعدادات الفئة'
          toast.error(message)
          
          set({ isLoading: false })
          return { success: false, error: message }
        }
      },

      updateSetting: async (key, value) => {
        set({ isLoading: true })

        try {
          const response = await api.put(`/settings/${key}`, { value })
          const updatedSetting = response.data.data.setting

          // Update the setting in the store
          const currentSettings = get().settings
          const category = updatedSetting.category
          
          set({
            settings: {
              ...currentSettings,
              [category]: currentSettings[category]?.map(setting =>
                setting.key === key ? updatedSetting : setting
              ) || [updatedSetting]
            },
            isLoading: false,
            lastUpdated: new Date().toISOString()
          })

          toast.success(response.data.message_ar || 'تم تحديث الإعداد بنجاح')
          return { success: true, setting: updatedSetting }
        } catch (error) {
          const message = error.response?.data?.message_ar || 'فشل في تحديث الإعداد'
          toast.error(message)
          
          set({ isLoading: false })
          return { success: false, error: message }
        }
      },

      bulkUpdateSettings: async (settingsArray) => {
        set({ isLoading: true })

        try {
          const response = await api.put('/settings/bulk/update', { settings: settingsArray })
          const updatedSettings = response.data.data.settings

          // Update multiple settings in the store
          const currentSettings = get().settings
          const newSettings = { ...currentSettings }

          updatedSettings.forEach(setting => {
            const category = setting.category
            if (!newSettings[category]) {
              newSettings[category] = []
            }
            
            const index = newSettings[category].findIndex(s => s.key === setting.key)
            if (index >= 0) {
              newSettings[category][index] = setting
            } else {
              newSettings[category].push(setting)
            }
          })

          set({
            settings: newSettings,
            isLoading: false,
            lastUpdated: new Date().toISOString()
          })

          toast.success(response.data.message_ar || 'تم تحديث الإعدادات بنجاح')
          return { success: true, settings: updatedSettings }
        } catch (error) {
          const message = error.response?.data?.message_ar || 'فشل في تحديث الإعدادات'
          toast.error(message)
          
          set({ isLoading: false })
          return { success: false, error: message }
        }
      },

      validateSetting: async (key, value) => {
        try {
          const response = await api.post(`/settings/${key}/validate`, { value })
          return response.data.data
        } catch (error) {
          const message = error.response?.data?.message_ar || 'فشل في التحقق من الإعداد'
          return { valid: false, error: message }
        }
      },

      // Helper functions
      getSetting: (key, defaultValue = null) => {
        const { settings, publicSettings } = get()
        
        // First check public settings
        if (publicSettings[key] !== undefined) {
          return publicSettings[key]
        }

        // Then check full settings
        for (const category in settings) {
          const setting = settings[category]?.find(s => s.key === key)
          if (setting) {
            return setting.value
          }
        }

        return defaultValue
      },

      getSettingsByCategory: (category) => {
        const { settings } = get()
        return settings[category] || []
      },

      // Specific setting getters for commonly used settings
      getShopName: () => get().getSetting('SHOP_NAME', 'محل البلايستيشن'),
      getCurrency: () => get().getSetting('CURRENCY', 'EGP'),
      getTimezone: () => get().getSetting('TIMEZONE', 'Africa/Cairo'),
      getTheme: () => get().getSetting('THEME', 'light'),
      getLanguage: () => get().getSetting('LANGUAGE', 'ar'),
      
      // Payment settings
      acceptsCash: () => get().getSetting('ACCEPT_CASH', true),
      acceptsCards: () => get().getSetting('ACCEPT_CARDS', true),
      enablesPrepaid: () => get().getSetting('ENABLE_PREPAID', true),
      
      // Notification settings
      soundNotificationsEnabled: () => get().getSetting('ENABLE_SOUND_NOTIFICATIONS', true),
      getSessionWarningTime: () => get().getSetting('SESSION_WARNING_TIME', 10),
      
      // Pricing settings
      getDefaultHourlyRate: () => get().getSetting('DEFAULT_HOURLY_RATE', 25),
      getTaxRate: () => get().getSetting('TAX_RATE', 0.14),
      getMinSessionDuration: () => get().getSetting('MINIMUM_SESSION_DURATION', 15),
      getMaxSessionDuration: () => get().getSetting('MAXIMUM_SESSION_DURATION', 480),

      // Format currency
      formatCurrency: (amount) => {
        const currency = get().getCurrency()
        const formatter = new Intl.NumberFormat('ar-EG', {
          style: 'currency',
          currency: currency,
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })
        return formatter.format(amount)
      },

      // Format time
      formatTime: (date) => {
        const timezone = get().getTimezone()
        return new Intl.DateTimeFormat('ar-EG', {
          timeZone: timezone,
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        }).format(new Date(date))
      },

      // Format date
      formatDate: (date) => {
        const timezone = get().getTimezone()
        return new Intl.DateTimeFormat('ar-EG', {
          timeZone: timezone,
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          weekday: 'long'
        }).format(new Date(date))
      },

      // Reset store
      reset: () => {
        set({
          settings: {},
          isLoading: false,
          lastUpdated: null
        })
      }
    }),
    {
      name: 'settings-storage',
      partialize: (state) => ({
        publicSettings: state.publicSettings,
        lastUpdated: state.lastUpdated
      })
    }
  )
)

export { useSettingsStore }
