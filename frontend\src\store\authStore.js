import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import toast from 'react-hot-toast'
import api from '../utils/api'

const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isLoading: false,
      isAuthenticated: false,

      // Actions
      login: async (credentials) => {
        set({ isLoading: true })
        
        try {
          const response = await api.post('/auth/login', credentials)
          const { token, user } = response.data.data

          // Store token in localStorage and set in API headers
          localStorage.setItem('token', token)
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`

          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false
          })

          toast.success(response.data.message_ar || 'تم تسجيل الدخول بنجاح')
          return { success: true, user }
        } catch (error) {
          const message = error.response?.data?.message_ar || 'فشل في تسجيل الدخول'
          toast.error(message)
          
          set({ isLoading: false })
          return { success: false, error: message }
        }
      },

      logout: async () => {
        try {
          // Call logout endpoint if token exists
          if (get().token) {
            await api.post('/auth/logout')
          }
        } catch (error) {
          // Ignore logout API errors
          console.warn('Logout API error:', error)
        } finally {
          // Clear local storage and state
          localStorage.removeItem('token')
          delete api.defaults.headers.common['Authorization']
          
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false
          })

          toast.success('تم تسجيل الخروج بنجاح')
        }
      },

      checkAuth: async () => {
        const token = localStorage.getItem('token')
        
        if (!token) {
          set({ isLoading: false, isAuthenticated: false })
          return false
        }

        set({ isLoading: true })

        try {
          // Set token in API headers
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`
          
          // Verify token with backend
          const response = await api.get('/auth/me')
          const user = response.data.data.user

          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false
          })

          return true
        } catch (error) {
          // Token is invalid, clear it
          localStorage.removeItem('token')
          delete api.defaults.headers.common['Authorization']
          
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false
          })

          return false
        }
      },

      updateProfile: async (profileData) => {
        set({ isLoading: true })

        try {
          const response = await api.put('/auth/me', profileData)
          const updatedUser = response.data.data.user

          set({
            user: updatedUser,
            isLoading: false
          })

          toast.success(response.data.message_ar || 'تم تحديث الملف الشخصي بنجاح')
          return { success: true, user: updatedUser }
        } catch (error) {
          const message = error.response?.data?.message_ar || 'فشل في تحديث الملف الشخصي'
          toast.error(message)
          
          set({ isLoading: false })
          return { success: false, error: message }
        }
      },

      changePassword: async (passwordData) => {
        set({ isLoading: true })

        try {
          const response = await api.put('/auth/change-password', passwordData)

          set({ isLoading: false })
          toast.success(response.data.message_ar || 'تم تغيير كلمة المرور بنجاح')
          return { success: true }
        } catch (error) {
          const message = error.response?.data?.message_ar || 'فشل في تغيير كلمة المرور'
          toast.error(message)
          
          set({ isLoading: false })
          return { success: false, error: message }
        }
      },

      // Helper functions
      hasPermission: (permission) => {
        const { user } = get()
        if (!user) return false
        
        // Admin has all permissions
        if (user.role === 'admin') return true
        
        // Check specific permission
        return user.permissions?.[permission] || false
      },

      isAdmin: () => {
        const { user } = get()
        return user?.role === 'admin'
      },

      isStaff: () => {
        const { user } = get()
        return user?.role === 'staff' || user?.role === 'admin'
      },

      // Reset store (useful for testing)
      reset: () => {
        localStorage.removeItem('token')
        delete api.defaults.headers.common['Authorization']
        
        set({
          user: null,
          token: null,
          isLoading: false,
          isAuthenticated: false
        })
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      }),
      onRehydrateStorage: () => (state) => {
        // Set token in API headers when rehydrating from storage
        if (state?.token) {
          api.defaults.headers.common['Authorization'] = `Bearer ${state.token}`
        }
      }
    }
  )
)

export { useAuthStore }
