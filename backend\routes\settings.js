const express = require('express');
const Joi = require('joi');
const { Settings } = require('../models');
const { 
  authenticateToken, 
  requirePermission 
} = require('../middleware/auth');
const { validateQuery } = require('../middleware/validation');
const { catchAsync, AppError, sendSuccess } = require('../middleware/errorHandler');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// @desc    Get all settings
// @route   GET /api/settings
// @access  Private
router.get('/',
  validateQuery(Joi.object({
    category: Joi.string().valid('general', 'pricing', 'payment', 'notifications', 'security', 'reports', 'ui').optional(),
    isVisible: Joi.boolean().optional()
  })),
  catchAsync(async (req, res, next) => {
    const { category, isVisible } = req.query;

    let settings;
    if (category) {
      settings = await Settings.getSettingsByCategory(category);
    } else {
      const query = {};
      if (isVisible !== undefined) query.isVisible = isVisible;
      
      settings = await Settings.find(query)
        .sort({ category: 1, key: 1 });
    }

    // Group settings by category
    const groupedSettings = settings.reduce((acc, setting) => {
      if (!acc[setting.category]) {
        acc[setting.category] = [];
      }
      acc[setting.category].push(setting);
      return acc;
    }, {});

    sendSuccess(res, 200, { settings: groupedSettings }, 'Settings retrieved successfully', 'تم استرداد الإعدادات بنجاح');
  })
);

// @desc    Get setting by key
// @route   GET /api/settings/:key
// @access  Private
router.get('/:key',
  catchAsync(async (req, res, next) => {
    const setting = await Settings.findOne({ key: req.params.key.toUpperCase() });

    if (!setting) {
      return next(new AppError('Setting not found', 404, 'الإعداد غير موجود'));
    }

    sendSuccess(res, 200, { setting }, 'Setting retrieved successfully', 'تم استرداد الإعداد بنجاح');
  })
);

// @desc    Update setting
// @route   PUT /api/settings/:key
// @access  Private (Requires canManageSettings permission)
router.put('/:key',
  requirePermission('canManageSettings'),
  catchAsync(async (req, res, next) => {
    const { value } = req.body;

    if (value === undefined) {
      return next(new AppError('Value is required', 400, 'القيمة مطلوبة'));
    }

    try {
      const setting = await Settings.setSetting(req.params.key, value, req.user._id);

      sendSuccess(res, 200, { setting }, 'Setting updated successfully', 'تم تحديث الإعداد بنجاح');
    } catch (error) {
      return next(new AppError(error.message, 400, error.message));
    }
  })
);

// @desc    Get settings by category
// @route   GET /api/settings/category/:category
// @access  Private
router.get('/category/:category',
  catchAsync(async (req, res, next) => {
    const { category } = req.params;

    const validCategories = ['general', 'pricing', 'payment', 'notifications', 'security', 'reports', 'ui'];
    if (!validCategories.includes(category)) {
      return next(new AppError('Invalid category', 400, 'فئة غير صحيحة'));
    }

    const settings = await Settings.getSettingsByCategory(category);

    sendSuccess(res, 200, { settings }, `${category} settings retrieved`, `تم استرداد إعدادات ${category}`);
  })
);

// @desc    Reset settings to default
// @route   POST /api/settings/reset
// @access  Private (Requires canManageSettings permission)
router.post('/reset',
  requirePermission('canManageSettings'),
  catchAsync(async (req, res, next) => {
    const { category, keys } = req.body;

    if (category) {
      // Reset all settings in a category
      const validCategories = ['general', 'pricing', 'payment', 'notifications', 'security', 'reports', 'ui'];
      if (!validCategories.includes(category)) {
        return next(new AppError('Invalid category', 400, 'فئة غير صحيحة'));
      }

      // This would require implementing default values restoration
      // For now, return an error
      return next(new AppError('Category reset not implemented', 501, 'إعادة تعيين الفئة غير مطبق'));
    }

    if (keys && Array.isArray(keys)) {
      // Reset specific settings
      // This would require implementing default values restoration
      // For now, return an error
      return next(new AppError('Key reset not implemented', 501, 'إعادة تعيين المفاتيح غير مطبق'));
    }

    return next(new AppError('Category or keys must be provided', 400, 'يجب توفير الفئة أو المفاتيح'));
  })
);

// @desc    Get public settings (for frontend)
// @route   GET /api/settings/public
// @access  Public
router.get('/public/config',
  catchAsync(async (req, res, next) => {
    // Get only public settings that frontend needs
    const publicSettings = await Settings.find({
      key: {
        $in: [
          'SHOP_NAME',
          'CURRENCY',
          'TIMEZONE',
          'ENABLE_SOUND_NOTIFICATIONS',
          'THEME',
          'LANGUAGE',
          'ACCEPT_CASH',
          'ACCEPT_CARDS',
          'ENABLE_PREPAID'
        ]
      },
      isVisible: true
    });

    // Convert to key-value pairs
    const config = publicSettings.reduce((acc, setting) => {
      acc[setting.key] = setting.value;
      return acc;
    }, {});

    sendSuccess(res, 200, { config }, 'Public settings retrieved', 'تم استرداد الإعدادات العامة');
  })
);

// @desc    Bulk update settings
// @route   PUT /api/settings/bulk
// @access  Private (Requires canManageSettings permission)
router.put('/bulk/update',
  requirePermission('canManageSettings'),
  catchAsync(async (req, res, next) => {
    const { settings } = req.body;

    if (!settings || !Array.isArray(settings)) {
      return next(new AppError('Settings array is required', 400, 'مصفوفة الإعدادات مطلوبة'));
    }

    const updatedSettings = [];
    const errors = [];

    for (const { key, value } of settings) {
      try {
        const setting = await Settings.setSetting(key, value, req.user._id);
        updatedSettings.push(setting);
      } catch (error) {
        errors.push({ key, error: error.message });
      }
    }

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Some settings failed to update',
        message_ar: 'فشل في تحديث بعض الإعدادات',
        data: { updatedSettings, errors }
      });
    }

    sendSuccess(res, 200, { settings: updatedSettings }, 'Settings updated successfully', 'تم تحديث الإعدادات بنجاح');
  })
);

// @desc    Get setting value only
// @route   GET /api/settings/:key/value
// @access  Private
router.get('/:key/value',
  catchAsync(async (req, res, next) => {
    const value = await Settings.getSetting(req.params.key);

    if (value === null) {
      return next(new AppError('Setting not found', 404, 'الإعداد غير موجود'));
    }

    sendSuccess(res, 200, { key: req.params.key.toUpperCase(), value }, 'Setting value retrieved', 'تم استرداد قيمة الإعداد');
  })
);

// @desc    Validate setting value
// @route   POST /api/settings/:key/validate
// @access  Private (Requires canManageSettings permission)
router.post('/:key/validate',
  requirePermission('canManageSettings'),
  catchAsync(async (req, res, next) => {
    const { value } = req.body;

    const setting = await Settings.findOne({ key: req.params.key.toUpperCase() });

    if (!setting) {
      return next(new AppError('Setting not found', 404, 'الإعداد غير موجود'));
    }

    try {
      // Validate the value without saving
      if (setting.type === 'number' && typeof value !== 'number') {
        throw new Error('القيمة يجب أن تكون رقم');
      }
      
      if (setting.type === 'boolean' && typeof value !== 'boolean') {
        throw new Error('القيمة يجب أن تكون true أو false');
      }
      
      if (setting.validationRules.min !== undefined && value < setting.validationRules.min) {
        throw new Error(`القيمة يجب أن تكون أكبر من أو تساوي ${setting.validationRules.min}`);
      }
      
      if (setting.validationRules.max !== undefined && value > setting.validationRules.max) {
        throw new Error(`القيمة يجب أن تكون أقل من أو تساوي ${setting.validationRules.max}`);
      }
      
      if (setting.validationRules.options && setting.validationRules.options.length > 0) {
        if (!setting.validationRules.options.includes(value)) {
          throw new Error('القيمة غير صحيحة');
        }
      }

      sendSuccess(res, 200, { valid: true }, 'Setting value is valid', 'قيمة الإعداد صحيحة');
    } catch (error) {
      sendSuccess(res, 200, { valid: false, error: error.message }, 'Setting value is invalid', 'قيمة الإعداد غير صحيحة');
    }
  })
);

module.exports = router;
