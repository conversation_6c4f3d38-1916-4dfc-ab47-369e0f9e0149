@echo off
chcp 65001 >nul
echo ========================================
echo    PlayStation Shop - تشغيل بدون قاعدة بيانات
echo    Running without Database
echo ========================================
echo.

echo هذا الملف سيشغل النظام بدون الحاجة لـ MongoDB
echo This will run the system without requiring MongoDB
echo.

REM Create a simple backend without MongoDB
echo إنشاء خادم مبسط / Creating simple server...

REM Create simple server file
(
echo const express = require('express'^);
echo const cors = require('cors'^);
echo const app = express(^);
echo const PORT = 5000;
echo.
echo app.use(cors(^));
echo app.use(express.json(^)^);
echo.
echo // Mock data
echo const mockUser = {
echo   id: '1',
echo   name: 'مدير النظام',
echo   email: '<EMAIL>',
echo   role: 'admin',
echo   permissions: {
echo     canManageConsoles: true,
echo     canManageSessions: true,
echo     canManageCustomers: true,
echo     canViewReports: true,
echo     canManageUsers: true,
echo     canManageSettings: true
echo   }
echo };
echo.
echo // Auth routes
echo app.post('/api/auth/login', (req, res^) =^> {
echo   const { email, password } = req.body;
echo   if (email === '<EMAIL>' ^&^& password === 'admin123456'^) {
echo     res.json({
echo       success: true,
echo       message: 'تم تسجيل الدخول بنجاح',
echo       message_ar: 'تم تسجيل الدخول بنجاح',
echo       data: {
echo         token: 'mock-jwt-token-12345',
echo         user: mockUser
echo       }
echo     }^);
echo   } else {
echo     res.status(401^).json({
echo       success: false,
echo       error: 'Invalid credentials',
echo       message_ar: 'بيانات دخول غير صحيحة'
echo     }^);
echo   }
echo }^);
echo.
echo app.get('/api/auth/me', (req, res^) =^> {
echo   res.json({
echo     success: true,
echo     data: { user: mockUser }
echo   }^);
echo }^);
echo.
echo // Dashboard data
echo app.get('/api/reports/analytics/dashboard', (req, res^) =^> {
echo   res.json({
echo     success: true,
echo     data: {
echo       summary: {
echo         totalConsoles: 8,
echo         totalCustomers: 45,
echo         totalRevenue: 12500
echo       },
echo       today: {
echo         totalSessions: 12,
echo         totalRevenue: 450,
echo         totalHours: 18.5
echo       },
echo       activeSessions: {
echo         count: 3,
echo         sessions: [
echo           {
echo             _id: '1',
echo             console: { name: 'PS5-001' },
echo             customerName: 'أحمد محمد',
echo             currentCost: 75,
echo             currentDuration: 90
echo           }
echo         ]
echo       }
echo     }
echo   }^);
echo }^);
echo.
echo // Settings
echo app.get('/api/settings/public/config', (req, res^) =^> {
echo   res.json({
echo     success: true,
echo     data: {
echo       config: {
echo         SHOP_NAME: 'محل البلايستيشن',
echo         CURRENCY: 'EGP',
echo         TIMEZONE: 'Africa/Cairo'
echo       }
echo     }
echo   }^);
echo }^);
echo.
echo // Health check
echo app.get('/api/health', (req, res^) =^> {
echo   res.json({
echo     status: 'OK',
echo     message: 'PlayStation Shop API is running (Mock Mode^)',
echo     message_ar: 'واجهة برمجة التطبيقات تعمل (وضع تجريبي^)'
echo   }^);
echo }^);
echo.
echo app.listen(PORT, (^) =^> {
echo   console.log(`🚀 Mock Server running on port ${PORT}`^);
echo   console.log('🚀 الخادم التجريبي يعمل على المنفذ ' + PORT^);
echo   console.log('💡 This is a simplified version without database'^);
echo   console.log('💡 هذا إصدار مبسط بدون قاعدة بيانات'^);
echo }^);
) > "backend\mock-server.js"

echo ✅ تم إنشاء الخادم المبسط / Mock server created

echo.
echo بدء تشغيل الخادم المبسط / Starting mock server...
cd backend
start "PlayStation Shop - Mock Backend" cmd /k "node mock-server.js"

echo انتظار بدء الخادم / Waiting for server to start...
timeout /t 3 /nobreak >nul

echo.
echo بدء تشغيل Frontend / Starting Frontend...
cd ..\frontend
start "PlayStation Shop - Frontend" cmd /k "npm run dev"

echo.
echo ========================================
echo ✅ النظام يعمل في الوضع التجريبي!
echo ✅ System running in demo mode!
echo ========================================
echo.
echo 🌐 الروابط / URLs:
echo   Frontend: http://localhost:3000
echo   Backend:  http://localhost:5000/api
echo.
echo 🔑 بيانات تسجيل الدخول / Login:
echo   Email:    <EMAIL>
echo   Password: admin123456
echo.
echo ⚠️ ملاحظة: هذا وضع تجريبي بدون قاعدة بيانات
echo ⚠️ Note: This is demo mode without database
echo    البيانات لن تحفظ / Data will not be saved
echo.
echo ========================================

timeout /t 10 /nobreak >nul
echo فتح المتصفح / Opening browser...
start http://localhost:3000

echo.
echo اضغط أي مفتاح للخروج / Press any key to exit...
pause >nul
