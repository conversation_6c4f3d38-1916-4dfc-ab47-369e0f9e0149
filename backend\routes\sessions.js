const express = require('express');
const Joi = require('joi');
const { Session, Console, Customer, Settings } = require('../models');
const { 
  authenticateToken, 
  requirePermission 
} = require('../middleware/auth');
const { validate, validateQuery, schemas, querySchemas } = require('../middleware/validation');
const { catchAsync, AppError, sendSuccess, sendPaginatedResponse } = require('../middleware/errorHandler');
const { dateUtils, numberUtils } = require('../utils/helpers');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// @desc    Get all sessions
// @route   GET /api/sessions
// @access  Private
router.get('/',
  validateQuery(Joi.object({
    ...querySchemas.pagination,
    status: Joi.string().valid('active', 'completed', 'cancelled', 'paused').optional(),
    console: Joi.string().optional(),
    customer: Joi.string().optional(),
    startDate: Joi.date().optional(),
    endDate: Joi.date().min(Joi.ref('startDate')).optional(),
    paymentStatus: Joi.string().valid('pending', 'paid', 'partial', 'refunded').optional()
  })),
  catchAsync(async (req, res, next) => {
    const { page, limit, sort, order, status, console, customer, startDate, endDate, paymentStatus } = req.query;

    // Build query
    const query = {};
    if (status) query.status = status;
    if (console) query.console = console;
    if (customer) query.customer = customer;
    if (paymentStatus) query.paymentStatus = paymentStatus;
    
    if (startDate || endDate) {
      query.startTime = {};
      if (startDate) query.startTime.$gte = new Date(startDate);
      if (endDate) query.startTime.$lte = new Date(endDate);
    }

    // Build sort object
    const sortObj = {};
    if (sort) {
      sortObj[sort] = order === 'asc' ? 1 : -1;
    } else {
      sortObj.startTime = -1; // Default sort by start time (newest first)
    }

    // Execute query with pagination
    const sessions = await Session.find(query)
      .populate('console', 'name consoleId type')
      .populate('customer', 'name phone membershipType')
      .populate('createdBy', 'name')
      .populate('endedBy', 'name')
      .sort(sortObj)
      .limit(limit)
      .skip((page - 1) * limit);

    const total = await Session.countDocuments(query);

    sendPaginatedResponse(res, sessions, page, limit, total, 'Sessions retrieved successfully', 'تم استرداد الجلسات بنجاح');
  })
);

// @desc    Get active sessions
// @route   GET /api/sessions/active
// @access  Private
router.get('/active',
  catchAsync(async (req, res, next) => {
    const sessions = await Session.getActiveSessions();

    sendSuccess(res, 200, { sessions }, 'Active sessions retrieved', 'تم استرداد الجلسات النشطة');
  })
);

// @desc    Get session by ID
// @route   GET /api/sessions/:id
// @access  Private
router.get('/:id',
  catchAsync(async (req, res, next) => {
    const session = await Session.findById(req.params.id)
      .populate('console', 'name consoleId type hourlyPrice')
      .populate('customer', 'name phone membershipType prepaidBalance')
      .populate('createdBy', 'name')
      .populate('endedBy', 'name');

    if (!session) {
      return next(new AppError('Session not found', 404, 'الجلسة غير موجودة'));
    }

    sendSuccess(res, 200, { session }, 'Session retrieved successfully', 'تم استرداد الجلسة بنجاح');
  })
);

// @desc    Start new session
// @route   POST /api/sessions
// @access  Private (Requires canManageSessions permission)
router.post('/',
  requirePermission('canManageSessions'),
  validate(schemas.startSession),
  catchAsync(async (req, res, next) => {
    const { consoleId, customerId, customerName, plannedDuration, notes } = req.body;

    // Find console
    const console = await Console.findById(consoleId);
    if (!console) {
      return next(new AppError('Console not found', 404, 'الجهاز غير موجود'));
    }

    // Check if console is available
    if (console.status !== 'available') {
      return next(new AppError('Console is not available', 400, 'الجهاز غير متاح'));
    }

    // Check if console already has an active session
    const existingSession = await Session.findOne({ 
      console: consoleId, 
      status: { $in: ['active', 'paused'] } 
    });
    
    if (existingSession) {
      return next(new AppError('Console already has an active session', 400, 'الجهاز له جلسة نشطة بالفعل'));
    }

    // Validate customer if provided
    let customer = null;
    if (customerId) {
      customer = await Customer.findById(customerId);
      if (!customer) {
        return next(new AppError('Customer not found', 404, 'العميل غير موجود'));
      }
      if (customer.isBlacklisted) {
        return next(new AppError('Customer is blacklisted', 400, 'العميل محظور'));
      }
    }

    // Get minimum session duration from settings
    const minDuration = await Settings.getSetting('MINIMUM_SESSION_DURATION') || 15;
    const maxDuration = await Settings.getSetting('MAXIMUM_SESSION_DURATION') || 480;

    if (plannedDuration && (plannedDuration < minDuration || plannedDuration > maxDuration)) {
      return next(new AppError(
        `Session duration must be between ${minDuration} and ${maxDuration} minutes`,
        400,
        `مدة الجلسة يجب أن تكون بين ${minDuration} و ${maxDuration} دقيقة`
      ));
    }

    // Create session
    const sessionData = {
      console: consoleId,
      customer: customerId || null,
      customerName: customerName || (customer ? customer.name : null),
      plannedDuration,
      hourlyRate: console.hourlyPrice,
      notes,
      createdBy: req.user._id
    };

    const session = await Session.create(sessionData);

    // Update console status to active
    console.status = 'active';
    await console.save();

    // Populate session data
    const populatedSession = await Session.findById(session._id)
      .populate('console', 'name consoleId type')
      .populate('customer', 'name phone membershipType')
      .populate('createdBy', 'name');

    sendSuccess(res, 201, { session: populatedSession }, 'Session started successfully', 'تم بدء الجلسة بنجاح');
  })
);

// @desc    End session
// @route   PUT /api/sessions/:id/end
// @access  Private (Requires canManageSessions permission)
router.put('/:id/end',
  requirePermission('canManageSessions'),
  validate(schemas.endSession),
  catchAsync(async (req, res, next) => {
    const { paymentMethod, discountAmount, discountReason, notes } = req.body;

    const session = await Session.findById(req.params.id)
      .populate('console')
      .populate('customer');

    if (!session) {
      return next(new AppError('Session not found', 404, 'الجلسة غير موجودة'));
    }

    if (session.status === 'completed') {
      return next(new AppError('Session is already completed', 400, 'الجلسة مكتملة بالفعل'));
    }

    if (session.status === 'cancelled') {
      return next(new AppError('Cannot end a cancelled session', 400, 'لا يمكن إنهاء جلسة ملغية'));
    }

    // Get tax rate from settings
    const taxRate = await Settings.getSetting('TAX_RATE') || 0.14;

    // End the session
    await session.endSession(req.user._id, paymentMethod);

    // Apply discount if provided
    if (discountAmount && discountAmount > 0) {
      session.discountAmount = discountAmount;
      session.discountReason = discountReason;
    }

    // Calculate tax
    const taxableAmount = session.totalCost - (session.discountAmount || 0);
    session.taxAmount = numberUtils.calculateTax(taxableAmount, taxRate);
    session.finalAmount = taxableAmount + session.taxAmount;

    // Add notes if provided
    if (notes) {
      session.notes = notes;
    }

    // Mark as paid if payment method is provided
    if (paymentMethod) {
      session.paymentStatus = 'paid';
    }

    await session.save();

    // Update console status back to available
    if (session.console) {
      session.console.status = 'available';
      await session.console.save();

      // Update console statistics
      await session.console.updateStatistics({
        duration: session.actualDuration / 60, // Convert to hours
        totalCost: session.finalAmount
      });
    }

    // Update customer statistics if customer exists
    if (session.customer) {
      await session.customer.updateStatistics({
        duration: session.actualDuration / 60, // Convert to hours
        totalCost: session.finalAmount
      });

      // Deduct from prepaid balance if payment method is prepaid
      if (paymentMethod === 'prepaid') {
        try {
          await session.customer.deductPrepaidBalance(session.finalAmount);
        } catch (error) {
          return next(new AppError(error.message, 400, error.message));
        }
      }
    }

    // Populate updated session
    const updatedSession = await Session.findById(session._id)
      .populate('console', 'name consoleId type')
      .populate('customer', 'name phone membershipType')
      .populate('createdBy', 'name')
      .populate('endedBy', 'name');

    sendSuccess(res, 200, { session: updatedSession }, 'Session ended successfully', 'تم إنهاء الجلسة بنجاح');
  })
);

// @desc    Pause session
// @route   PUT /api/sessions/:id/pause
// @access  Private (Requires canManageSessions permission)
router.put('/:id/pause',
  requirePermission('canManageSessions'),
  catchAsync(async (req, res, next) => {
    const { reason } = req.body;

    const session = await Session.findById(req.params.id);

    if (!session) {
      return next(new AppError('Session not found', 404, 'الجلسة غير موجودة'));
    }

    await session.pauseSession(reason, req.user._id);

    sendSuccess(res, 200, { session }, 'Session paused successfully', 'تم إيقاف الجلسة مؤقتاً بنجاح');
  })
);

// @desc    Resume session
// @route   PUT /api/sessions/:id/resume
// @access  Private (Requires canManageSessions permission)
router.put('/:id/resume',
  requirePermission('canManageSessions'),
  catchAsync(async (req, res, next) => {
    const session = await Session.findById(req.params.id);

    if (!session) {
      return next(new AppError('Session not found', 404, 'الجلسة غير موجودة'));
    }

    await session.resumeSession(req.user._id);

    sendSuccess(res, 200, { session }, 'Session resumed successfully', 'تم استئناف الجلسة بنجاح');
  })
);

// @desc    Cancel session
// @route   PUT /api/sessions/:id/cancel
// @access  Private (Requires canManageSessions permission)
router.put('/:id/cancel',
  requirePermission('canManageSessions'),
  catchAsync(async (req, res, next) => {
    const { reason } = req.body;

    const session = await Session.findById(req.params.id).populate('console');

    if (!session) {
      return next(new AppError('Session not found', 404, 'الجلسة غير موجودة'));
    }

    if (session.status === 'completed') {
      return next(new AppError('Cannot cancel a completed session', 400, 'لا يمكن إلغاء جلسة مكتملة'));
    }

    // Cancel the session
    session.status = 'cancelled';
    session.endTime = new Date();
    session.notes = reason || 'Session cancelled';
    session.endedBy = req.user._id;

    await session.save();

    // Update console status back to available
    if (session.console) {
      session.console.status = 'available';
      await session.console.save();
    }

    sendSuccess(res, 200, { session }, 'Session cancelled successfully', 'تم إلغاء الجلسة بنجاح');
  })
);

// @desc    Get session statistics
// @route   GET /api/sessions/stats
// @access  Private
router.get('/analytics/stats',
  validateQuery(Joi.object({
    period: Joi.string().valid('today', 'yesterday', 'week', 'month', 'year').default('today'),
    startDate: Joi.date().optional(),
    endDate: Joi.date().min(Joi.ref('startDate')).optional()
  })),
  catchAsync(async (req, res, next) => {
    const { period, startDate, endDate } = req.query;

    let queryStartDate, queryEndDate;

    if (startDate && endDate) {
      queryStartDate = new Date(startDate);
      queryEndDate = new Date(endDate);
    } else {
      const now = new Date();
      switch (period) {
        case 'today':
          queryStartDate = dateUtils.getStartOfDay(now);
          queryEndDate = dateUtils.getEndOfDay(now);
          break;
        case 'yesterday':
          const yesterday = new Date(now);
          yesterday.setDate(yesterday.getDate() - 1);
          queryStartDate = dateUtils.getStartOfDay(yesterday);
          queryEndDate = dateUtils.getEndOfDay(yesterday);
          break;
        case 'week':
          queryStartDate = dateUtils.getStartOfWeek(now);
          queryEndDate = dateUtils.getEndOfWeek(now);
          break;
        case 'month':
          queryStartDate = dateUtils.getStartOfMonth(now);
          queryEndDate = dateUtils.getEndOfMonth(now);
          break;
        case 'year':
          queryStartDate = dateUtils.getStartOfYear(now);
          queryEndDate = dateUtils.getEndOfYear(now);
          break;
        default:
          queryStartDate = dateUtils.getStartOfDay(now);
          queryEndDate = dateUtils.getEndOfDay(now);
      }
    }

    const stats = await Session.getDailyStats(queryStartDate);

    sendSuccess(res, 200, { stats, period, startDate: queryStartDate, endDate: queryEndDate }, 'Session statistics retrieved', 'تم استرداد إحصائيات الجلسات');
  })
);

module.exports = router;
