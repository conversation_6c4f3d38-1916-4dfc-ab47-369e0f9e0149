const mongoose = require('mongoose');

const consoleSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'اسم الجهاز مطلوب'],
    trim: true,
    maxlength: [50, 'اسم الجهاز يجب أن يكون أقل من 50 حرف']
  },
  consoleId: {
    type: String,
    required: [true, 'رقم الجهاز مطلوب'],
    unique: true,
    trim: true,
    uppercase: true,
    match: [/^[A-Z0-9-]+$/, 'رقم الجهاز يجب أن يحتوي على أحرف إنجليزية وأرقام فقط']
  },
  type: {
    type: String,
    required: [true, 'نوع الجهاز مطلوب'],
    enum: {
      values: ['PS5', 'PS4', 'PS4_PRO', 'XBOX_SERIES_X', 'XBOX_SERIES_S', 'XBOX_ONE', 'PC', 'NINTENDO_SWITCH'],
      message: 'نوع الجهاز غير صحيح'
    }
  },
  hourlyPrice: {
    type: Number,
    required: [true, 'سعر الساعة مطلوب'],
    min: [0, 'سعر الساعة يجب أن يكون أكبر من أو يساوي صفر'],
    validate: {
      validator: function(value) {
        return value >= 0 && Number.isFinite(value);
      },
      message: 'سعر الساعة يجب أن يكون رقم صحيح'
    }
  },
  status: {
    type: String,
    enum: {
      values: ['available', 'active', 'maintenance', 'offline'],
      message: 'حالة الجهاز غير صحيحة'
    },
    default: 'available'
  },
  location: {
    type: String,
    trim: true,
    maxlength: [100, 'موقع الجهاز يجب أن يكون أقل من 100 حرف']
  },
  specifications: {
    storage: { type: String, trim: true },
    controllers: { type: Number, min: 1, max: 8, default: 2 },
    accessories: [{ type: String, trim: true }],
    notes: { type: String, trim: true, maxlength: [500, 'الملاحظات يجب أن تكون أقل من 500 حرف'] }
  },
  maintenance: {
    lastMaintenanceDate: { type: Date },
    nextMaintenanceDate: { type: Date },
    maintenanceNotes: { type: String, trim: true, maxlength: [1000, 'ملاحظات الصيانة يجب أن تكون أقل من 1000 حرف'] },
    isUnderMaintenance: { type: Boolean, default: false }
  },
  statistics: {
    totalSessions: { type: Number, default: 0 },
    totalHours: { type: Number, default: 0 },
    totalRevenue: { type: Number, default: 0 },
    averageSessionDuration: { type: Number, default: 0 },
    lastUsed: { type: Date }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
consoleSchema.index({ consoleId: 1 });
consoleSchema.index({ type: 1 });
consoleSchema.index({ status: 1 });
consoleSchema.index({ isActive: 1 });
consoleSchema.index({ 'statistics.totalRevenue': -1 });
consoleSchema.index({ 'statistics.totalHours': -1 });

// Virtual for current session (if any)
consoleSchema.virtual('currentSession', {
  ref: 'Session',
  localField: '_id',
  foreignField: 'console',
  justOne: true,
  match: { status: 'active' }
});

// Virtual for Arabic status display
consoleSchema.virtual('statusArabic').get(function() {
  const statusMap = {
    'available': 'متاح',
    'active': 'نشط',
    'maintenance': 'صيانة',
    'offline': 'غير متصل'
  };
  return statusMap[this.status] || this.status;
});

// Virtual for Arabic type display
consoleSchema.virtual('typeArabic').get(function() {
  const typeMap = {
    'PS5': 'بلايستيشن 5',
    'PS4': 'بلايستيشن 4',
    'PS4_PRO': 'بلايستيشن 4 برو',
    'XBOX_SERIES_X': 'إكس بوكس سيريس إكس',
    'XBOX_SERIES_S': 'إكس بوكس سيريس إس',
    'XBOX_ONE': 'إكس بوكس ون',
    'PC': 'كمبيوتر',
    'NINTENDO_SWITCH': 'نينتندو سويتش'
  };
  return typeMap[this.type] || this.type;
});

// Method to update statistics
consoleSchema.methods.updateStatistics = async function(sessionData) {
  this.statistics.totalSessions += 1;
  this.statistics.totalHours += sessionData.duration;
  this.statistics.totalRevenue += sessionData.totalCost;
  this.statistics.lastUsed = new Date();
  
  // Calculate average session duration
  this.statistics.averageSessionDuration = this.statistics.totalHours / this.statistics.totalSessions;
  
  await this.save();
};

// Static method to get available consoles
consoleSchema.statics.getAvailableConsoles = function() {
  return this.find({ 
    status: 'available', 
    isActive: true,
    'maintenance.isUnderMaintenance': false 
  }).sort({ name: 1 });
};

// Static method to get console statistics
consoleSchema.statics.getStatistics = async function() {
  const stats = await this.aggregate([
    { $match: { isActive: true } },
    {
      $group: {
        _id: null,
        totalConsoles: { $sum: 1 },
        availableConsoles: { 
          $sum: { 
            $cond: [{ $eq: ['$status', 'available'] }, 1, 0] 
          } 
        },
        activeConsoles: { 
          $sum: { 
            $cond: [{ $eq: ['$status', 'active'] }, 1, 0] 
          } 
        },
        maintenanceConsoles: { 
          $sum: { 
            $cond: [{ $eq: ['$status', 'maintenance'] }, 1, 0] 
          } 
        },
        totalRevenue: { $sum: '$statistics.totalRevenue' },
        totalHours: { $sum: '$statistics.totalHours' }
      }
    }
  ]);
  
  return stats[0] || {
    totalConsoles: 0,
    availableConsoles: 0,
    activeConsoles: 0,
    maintenanceConsoles: 0,
    totalRevenue: 0,
    totalHours: 0
  };
};

module.exports = mongoose.model('Console', consoleSchema);
